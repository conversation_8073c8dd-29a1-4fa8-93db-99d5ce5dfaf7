# PRD - 直角弯停车问题根因分析

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-08-01 |
| 负责人 | Emma & Bob |
| 项目名称 | TI_CAR1.9 直角弯停车问题根因分析 |
| 文档状态 | 根因定位完成 |

## 2. 问题现象

### 2.1 用户反馈
- **现象**: 小车在转直角弯时直接停在那里
- **观察**: 左右电机目标值始终正确
- **疑问**: 为什么目标值正确但小车不动

### 2.2 关键观察点
- 电机目标值设置正确：`Motor_Font_Left.Motor_PID_Instance.Target = 0`, `Motor_Font_Right.Motor_PID_Instance.Target = 8`
- 小车完全停止，没有任何运动
- 直角弯检测逻辑正常工作（LED1亮起）

## 3. 根因分析

### 3.1 🚨 **核心问题：逻辑冲突导致目标值被覆盖**

#### 问题1: `is_turning`变量重复定义
**位置**: `BSP/Src/Tracker.c`

<augment_code_snippet path="BSP/Src/Tracker.c" mode="EXCERPT">
```c
// 第7行：全局变量定义
bool is_turning = false;

// 第22行：函数内static变量定义
static bool is_turning = false;  // ❌ 重复定义！
```
</augment_code_snippet>

**问题影响**:
- 函数内的`static is_turning`会屏蔽全局变量
- 导致外部无法正确读取转弯状态
- `Task_Motor_PID`中的`if(!is_turning)`判断失效

#### 问题2: 目标值设置被条件阻止
**位置**: `APP/Src/Task_App.c` 第126-131行

<augment_code_snippet path="APP/Src/Task_App.c" mode="EXCERPT">
```c
if(!is_turning)  // ❌ 由于is_turning变量问题，这个条件可能永远为true
{
    // 设置目标速度
    Motor_Font_Left.Motor_PID_Instance.Target =  _IQtoF(Left_Speed);
    Motor_Font_Right.Motor_PID_Instance.Target =  _IQtoF(Right_Speed);
}
```
</augment_code_snippet>

**问题影响**:
- 当`is_turning`状态错误时，正常循迹的目标值会覆盖转弯目标值
- 导致转弯时设置的目标值立即被重置

#### 问题3: 转弯检测条件错误
**位置**: `BSP/Src/Tracker.c` 第48行

<augment_code_snippet path="BSP/Src/Tracker.c" mode="EXCERPT">
```c
// 当前检测条件
if(tick[0]==1 && tick[1]==1 && tick[2]==1 && tick[3]==0 &&
   tick[4]==0 && tick[5]==0 && tick[6]==0)

// 问题：tick[3]==0 表示中心传感器没检测到线
// 但直角弯时，中心传感器通常应该检测到线
```
</augment_code_snippet>

### 3.2 执行时序分析

#### 时序问题流程
1. **直角弯检测触发** → 设置转弯目标值 (`Target = 0/8`)
2. **`Tracker_Read`函数返回** → 继续执行`Task_Motor_PID`
3. **`Task_Motor_PID`执行** → 由于`is_turning`状态错误，执行`if(!is_turning)`分支
4. **目标值被覆盖** → 转弯目标值被正常循迹目标值覆盖
5. **结果** → 小车停止运动

#### 调用链分析
```
Task_Tracker (20ms) 
  └── Tracker_Read()
      ├── 检测直角弯 ✓
      ├── 设置转弯目标值 ✓  
      └── 设置is_turning = true ❌ (被static变量屏蔽)

Task_Motor_PID (50ms)
  ├── 读取is_turning状态 ❌ (读取到错误状态)
  ├── 执行if(!is_turning)分支 ❌ 
  ├── 覆盖目标值 ❌
  └── PID计算 → 输出0 → 小车停止
```

## 4. 验证分析

### 4.1 变量作用域验证
**全局变量**: `BSP/Src/Tracker.c` 第7行
```c
bool is_turning = false;  // 全局作用域
```

**局部变量**: `BSP/Src/Tracker.c` 第22行
```c
static bool is_turning = false;  // 函数作用域，屏蔽全局变量
```

**外部声明**: `BSP/Inc/Tracker.h` 第7行
```c
extern bool is_turning;  // 引用全局变量
```

**使用位置**: `APP/Src/Task_App.c` 第126行
```c
if(!is_turning)  // 引用全局变量，但被函数内static变量影响
```

### 4.2 内存映射验证
**编译器映射文件显示**:
```
202006f9    00000001     Tracker.o (.data.Tracker_Read.is_turning)  // static变量
202006fa    00000001     Tracker.o (.data.is_turning)               // 全局变量
```

**结论**: 确实存在两个独立的`is_turning`变量

## 5. 解决方案

### 5.1 🔴 **立即修复 (高优先级)**

#### 修复1: 删除重复的static变量定义
**位置**: `BSP/Src/Tracker.c` 第22行
```c
// 删除这一行
static bool is_turning = false;  // ❌ 删除

// 同时删除第21行的static变量
static uint32_t turn_start_time = 0;  // ❌ 删除
```

#### 修复2: 使用全局变量
**位置**: `BSP/Src/Tracker.c` 顶部添加
```c
// 在文件顶部添加全局变量
uint32_t turn_start_time = 0;  // ✅ 全局变量
// is_turning已经在第7行定义，无需重复
```

#### 修复3: 修正转弯检测条件
**位置**: `BSP/Src/Tracker.c` 第48行
```c
// 当前错误条件
if(tick[0]==1 && tick[1]==1 && tick[2]==1 && tick[3]==0 &&
   tick[4]==0 && tick[5]==0 && tick[6]==0)

// 修正为（根据实际传感器布局）
if(tick[0]==1 && tick[1]==1 && tick[2]==1 && tick[3]==1 &&
   tick[4]==0 && tick[5]==0 && tick[6]==0)
```

### 5.2 🟡 **逻辑优化 (中优先级)**

#### 优化1: 改进状态管理
```c
// 在Tracker.h中添加状态枚举
typedef enum {
    TRACK_NORMAL = 0,
    TRACK_TURNING_RIGHT,
    TRACK_TURNING_LEFT
} TrackState_t;

extern TrackState_t track_state;
```

#### 优化2: 统一状态检查
```c
// 在Task_Motor_PID中
if(track_state == TRACK_NORMAL)
{
    // 正常循迹目标值设置
    Motor_Font_Left.Motor_PID_Instance.Target =  _IQtoF(Left_Speed);
    Motor_Font_Right.Motor_PID_Instance.Target =  _IQtoF(Right_Speed);
}
// 转弯状态下不覆盖目标值
```

## 6. 修改步骤

### 6.1 第一步：修复变量冲突
1. 打开 `BSP/Src/Tracker.c`
2. 删除第21-22行的static变量定义
3. 在文件顶部添加全局变量 `uint32_t turn_start_time = 0;`

### 6.2 第二步：修正检测条件
1. 修改第48行的转弯检测条件
2. 将 `tick[3]==0` 改为 `tick[3]==1`

### 6.3 第三步：验证修复
1. 编译并运行
2. 观察OLED显示的传感器状态
3. 确认转弯时LED1亮起且小车正常转弯

## 7. 预期效果

### 7.1 修复后的执行流程
1. **直角弯检测触发** → 设置转弯目标值
2. **全局`is_turning = true`** → 状态正确设置
3. **`Task_Motor_PID`读取状态** → 正确识别转弯状态
4. **跳过目标值覆盖** → 保持转弯目标值
5. **PID计算** → 输出正确的PWM值
6. **小车正常转弯** → 问题解决

### 7.2 性能改善
- ✅ 小车能够正常执行直角弯转向
- ✅ 转弯过程平滑，无停顿
- ✅ 转弯完成后能正常恢复循迹
- ✅ 系统状态管理更加可靠

## 8. 风险提示

### 8.1 修改风险
- **低风险**: 变量作用域修复是安全的修改
- **测试重点**: 确认转弯检测条件符合实际传感器布局
- **回退方案**: 保留原始代码备份

### 8.2 验证要点
- 确认转弯时LED1正常亮起
- 观察OLED显示的传感器状态是否符合预期
- 测试转弯完成后的恢复逻辑

## 9. 总结

**根本原因**: `is_turning`变量的重复定义导致状态管理混乱，进而引起目标值被错误覆盖。

**解决核心**: 删除函数内的static变量定义，使用全局变量进行状态管理。

**修改影响**: 最小化修改，风险极低，效果立竿见影。
