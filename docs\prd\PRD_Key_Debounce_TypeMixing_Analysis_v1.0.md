# PRD - 按键消抖和类型混用问题分析

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-07-31 |
| 负责人 | Emma (产品经理) |
| 项目名称 | TI_CAR1.4 按键消抖机制和IQ库类型混用检查 |
| 文档状态 | 需求分析完成 |

## 2. 背景与问题陈述

### 2.1 问题描述
在TI_CAR1.4智能小车项目中，需要检查按键消抖机制的完善性和IQ库参数类型混用问题，确保代码质量和系统稳定性。

### 2.2 当前实现分析

#### 2.2.1 按键硬件配置
- **按键数量**: 3个按键 (KEY2, KEY3, KEY4)
- **按键类型**: 机械按键，低电平有效
- **硬件连接**: 
  - KEY2: GPIOA端口
  - KEY3: GPIOA端口  
  - KEY4: GPIOA端口

#### 2.2.2 当前按键处理实现
```c
// 当前实现 - Task_App.c
void Task_Key(void *para)
{
    static uint8_t Key_Old;
    uint8_t Key_Temp = Key_Read();
    uint8_t Key_Val = (Key_Temp ^ Key_Old) & Key_Temp;  // 边沿检测
    Key_Old = Key_Temp;
    
    if (Key_Val==2) {
        Data_Motor_TarSpeed+=5;  // 类型混用问题！
        // LED控制...
    }
    else if(Key_Val==3) {
        Data_Motor_TarSpeed-=5;  // 类型混用问题！
        // LED控制...
    }
    else {
        Data_Motor_TarSpeed=0;   // 类型混用问题！
        // LED控制...
    }
}
```

#### 2.2.3 发现的关键问题

**🚨 严重类型混用问题**:
1. `Data_Motor_TarSpeed` 定义为 `uint16_t` 类型
2. 但在电机控制中与 `_iq` 类型混合运算
3. 缺少正确的类型转换

**🚨 按键消抖问题**:
1. 仅使用简单边沿检测，无时间延迟消抖
2. 20ms任务周期可能不足以处理机械按键抖动
3. 缺少按键状态机和防重复触发机制

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **完善按键消抖**: 实现可靠的按键消抖机制
2. **修复类型混用**: 统一数据类型，消除类型混用问题
3. **提升用户体验**: 确保按键响应准确、无误触发

### 3.2 关键结果 (Key Results)
1. **按键响应准确性**: 按键误触发率 < 0.1%
2. **消抖时间**: 消抖延迟 ≤ 50ms
3. **类型一致性**: 消除所有类型混用警告
4. **代码质量**: 通过静态代码分析检查

### 3.3 反向指标 (Counter Metrics)
1. **响应延迟**: 按键响应延迟不超过100ms
2. **内存占用**: 消抖机制内存开销 < 100字节
3. **CPU占用**: 按键处理CPU占用 < 1%

## 4. 用户画像与用户故事

### 4.1 目标用户
- **操作人员**: 需要通过按键控制小车运动参数
- **测试人员**: 需要可靠的按键响应进行功能测试
- **开发人员**: 需要稳定的按键接口进行调试

### 4.2 用户故事
- **作为操作人员**, 我希望按键响应准确，这样我就能精确控制小车速度
- **作为测试人员**, 我希望按键不会误触发，这样我就能进行可靠的功能测试
- **作为开发人员**, 我希望代码类型安全，这样我就能避免潜在的运行时错误

## 5. 功能规格详述

### 5.1 按键消抖需求

#### 5.1.1 硬件消抖 vs 软件消抖
- **当前状态**: 仅软件处理，无硬件消抖电路
- **需求**: 实现完善的软件消抖机制
- **消抖时间**: 20-50ms标准消抖时间

#### 5.1.2 消抖算法要求
1. **时间延迟消抖**: 按键状态稳定持续一定时间后才确认
2. **状态机消抖**: 实现按键状态机，防止重复触发
3. **多按键处理**: 支持多按键同时按下的处理

#### 5.1.3 按键功能定义
- **KEY2**: 增加目标速度 (+5)
- **KEY3**: 减少目标速度 (-5)  
- **KEY4**: 停止/复位速度 (=0)
- **组合键**: 预留组合键功能扩展

### 5.2 类型混用修复需求

#### 5.2.1 数据类型统一
**问题**: `Data_Motor_TarSpeed` 类型不一致
```c
// 当前错误定义
uint16_t Data_Motor_TarSpeed = 20;  // 整型

// 但在使用中与IQ类型混用
_iq Left_Speed = Steering_Adjustment;
Motor_Font_Left.Motor_PID_Instance.Target = Data_Motor_TarSpeed + _IQtoF(Left_Speed);
```

**解决方案**: 统一使用合适的数据类型

#### 5.2.2 IQ库使用规范
1. **速度相关变量**: 统一使用 `_iq` 或 `float` 类型
2. **运算函数**: 正确使用 `_IQmpy()`, `_IQdiv()` 等函数
3. **类型转换**: 明确使用 `_IQ()`, `_IQtoF()` 转换函数

#### 5.2.3 PID系统类型一致性
- **PID结构体**: 当前使用 `float` 类型
- **目标值设置**: 需要确保类型匹配
- **输出值处理**: 统一数据类型流

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- 按键消抖机制完善
- 数据类型混用修复
- 按键状态机实现
- 代码质量检查和优化
- 用户体验改进

### 6.2 排除功能 (Out of Scope)
- 硬件按键电路修改
- 按键数量增加
- 按键功能重新定义
- 完全重写按键驱动架构

## 7. 技术实现方案

### 7.1 按键消抖优化方案

#### 方案A: 时间延迟消抖
```c
typedef struct {
    uint8_t current_state;
    uint8_t last_state;
    uint32_t debounce_time;
    uint32_t last_change_time;
    bool is_pressed;
} Key_Debounce_t;
```

#### 方案B: 状态机消抖
```c
typedef enum {
    KEY_IDLE,
    KEY_PRESS_DETECTED,
    KEY_PRESSED,
    KEY_RELEASE_DETECTED
} Key_State_t;
```

### 7.2 类型混用修复方案

#### 方案A: 全部使用float类型
```c
float Data_Motor_TarSpeed = 20.0f;  // 统一使用float
```

#### 方案B: 全部使用IQ类型
```c
_iq Data_Motor_TarSpeed = _IQ(20);  // 统一使用IQ
```

## 8. 依赖与风险

### 8.1 技术依赖
- TI IQmath库正确配置
- 系统时钟精度要求
- 任务调度器稳定性

### 8.2 潜在风险
| 风险 | 影响 | 概率 | 缓解措施 |
|------|------|------|----------|
| 消抖时间过长影响响应性 | 中 | 低 | 优化消抖算法参数 |
| 类型转换引入精度损失 | 中 | 中 | 选择合适的数据类型 |
| 内存占用增加 | 低 | 低 | 优化数据结构设计 |

## 9. 验收标准

### 9.1 功能验收
- [ ] 按键响应准确，无误触发
- [ ] 消抖机制工作正常
- [ ] 所有类型混用问题已修复
- [ ] 代码通过静态分析检查

### 9.2 性能验收
- [ ] 按键响应时间 ≤ 100ms
- [ ] 误触发率 < 0.1%
- [ ] CPU占用增加 < 1%
- [ ] 内存占用增加 < 100字节

### 9.3 质量验收
- [ ] 代码符合编码规范
- [ ] 注释完整清晰
- [ ] 无编译警告
- [ ] 通过单元测试

## 10. 版本历史

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| v1.0 | 2025-07-31 | 初版创建，完成需求分析和问题识别 | Emma |

---

**文档状态**: ✅ 需求分析完成  
**关键发现**: 发现严重的类型混用问题和不完善的按键消抖机制  
**下一步**: 进入硬件配置和架构检查阶段
