# PRD - 直角弯循迹问题分析与优化建议

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-08-01 |
| 负责人 | Emma & Bob |
| 项目名称 | TI_CAR1.9 直角弯循迹问题分析 |
| 文档状态 | 问题诊断完成 |

## 2. 当前工程状态分析

### 2.1 发现的关键问题

#### 🚨 **问题1: 修正算法被简化**
```c
// 当前实现 (第119行)
_iq Steering_Adjustment = Data_Tracker_Offset;  // ❌ 直接使用偏差值

// 应该是
_iq Steering_Adjustment = _IQmpy(Data_Tracker_Offset, _IQ(INDEX));  // ✅ 乘以修正系数
```

#### 🚨 **问题2: 直角弯特殊处理逻辑有缺陷**
```c
// 第45-55行的直角弯检测逻辑
if(tick[0]==1 && tick[1]==1 && tick[2]==1 && tick[3]==1 &&
   tick[4]==0 && tick[5]==0 && tick[6]==0)
{
    Motor_Font_Left.Motor_PID_Instance.Target=0;   // 左轮停止
    Motor_Font_Right.Motor_PID_Instance.Target=5;  // 右轮5速度
    LED1_ON();
    Delay(5000);  // ❌ 阻塞延时5秒！
    LED1_OFF();
    Motor_Font_Left.Motor_PID_Instance.Target=0;   // 重复设置
    Motor_Font_Right.Motor_PID_Instance.Target=5;
}
```

#### 🚨 **问题3: 传感器权重计算注释错误**
```c
// 第70行注释错误
// 传感器位置权重: 0,1,2,3,4,5,6,7 对应 -5.25,-3.75,-2.25,-0.75,0.75,2.25,3.75,5.25 cm
// 实际应该是: 0,1,2,3,4,5,6 对应 -4.5,-3.0,-1.5,0,1.5,3.0,4.5 cm
```

### 2.2 直角弯循迹需求分析

#### 2.2.1 直角弯的特征
- **传感器模式**: 通常是前4个传感器检测到线，后3个传感器检测不到
- **转向需求**: 需要快速且精确的转向响应
- **速度控制**: 转弯时需要差速控制，外侧轮快，内侧轮慢

#### 2.2.2 当前算法的问题
1. **阻塞延时**: `Delay(5000)` 会阻塞整个系统5秒
2. **固定速度**: 硬编码速度值，缺乏灵活性
3. **重复设置**: 延时后重复设置相同的速度值
4. **缺乏退出机制**: 没有检测转弯完成的逻辑

## 3. 问题影响分析

### 3.1 修正算法简化的影响
- **修正不足**: 直接使用偏差值可能导致修正幅度过小
- **响应迟缓**: 对偏差的响应可能不够敏感
- **循迹精度下降**: 可能无法精确跟踪黑线

### 3.2 直角弯处理的影响
- **系统阻塞**: 5秒延时期间无法响应其他任务
- **转弯不精确**: 固定时间转弯可能导致转弯过度或不足
- **实时性丢失**: 无法根据实际情况调整转弯策略

## 4. 优化建议

### 4.1 修正算法修复

#### 建议1: 恢复修正系数
```c
// 在 APP/Src/Task_App.c 第119行修改
// 当前错误实现
_iq Steering_Adjustment = Data_Tracker_Offset;

// 建议修改为
_iq Steering_Adjustment = _IQmpy(Data_Tracker_Offset, _IQ(INDEX));
```

#### 建议2: 调整修正系数
```c
// 第13行，针对直角弯优化
#define INDEX 0.8f  // 从1.5f降低到0.8f，避免过度修正
```

### 4.2 直角弯处理优化

#### 建议3: 非阻塞式直角弯处理
```c
// 在 BSP/Src/Tracker.c 第45-55行替换为
static uint32_t turn_start_time = 0;
static bool is_turning = false;

if(tick[0]==1 && tick[1]==1 && tick[2]==1 && tick[3]==1 &&
   tick[4]==0 && tick[5]==0 && tick[6]==0)
{
    if (!is_turning) {
        // 开始转弯
        is_turning = true;
        turn_start_time = SysTick_GetTick();  // 获取当前时间
        LED1_ON();
    }
    
    // 非阻塞转弯控制
    Motor_Font_Left.Motor_PID_Instance.Target = 0;
    Motor_Font_Right.Motor_PID_Instance.Target = 8;  // 提高转弯速度
    
    // 检查转弯时间或传感器状态来结束转弯
    if (SysTick_GetTick() - turn_start_time > 1000) {  // 1秒后检查
        // 检查是否完成转弯 (可以根据传感器状态判断)
        if (tick[3] == 1) {  // 中心传感器重新检测到线
            is_turning = false;
            LED1_OFF();
        }
    }
}
else if (is_turning) {
    // 转弯过程中，继续转弯动作
    Motor_Font_Left.Motor_PID_Instance.Target = 0;
    Motor_Font_Right.Motor_PID_Instance.Target = 8;
}
else {
    // 正常循迹模式
    is_turning = false;
    LED1_OFF();
    // 继续执行原有的循迹逻辑...
}
```

#### 建议4: 改进转弯检测条件
```c
// 更灵活的直角弯检测
bool is_right_turn = (tick[0]==1 && tick[1]==1 && tick[2]==1 && tick[3]==1 &&
                      tick[4]==0 && tick[5]==0 && tick[6]==0);

bool is_left_turn = (tick[0]==0 && tick[1]==0 && tick[2]==0 && tick[3]==1 &&
                     tick[4]==1 && tick[5]==1 && tick[6]==1);

if (is_right_turn) {
    // 右转逻辑
    Motor_Font_Left.Motor_PID_Instance.Target = 0;
    Motor_Font_Right.Motor_PID_Instance.Target = 8;
} else if (is_left_turn) {
    // 左转逻辑
    Motor_Font_Left.Motor_PID_Instance.Target = 8;
    Motor_Font_Right.Motor_PID_Instance.Target = 0;
}
```

### 4.3 参数优化建议

#### 建议5: 直角弯专用参数
```c
// 在文件顶部添加直角弯参数
#define TURN_SPEED 8        // 转弯时的电机速度
#define TURN_TIMEOUT 1500   // 转弯超时时间 (ms)
#define TURN_INDEX 1.2f     // 转弯时的修正系数 (比正常循迹大)
```

#### 建议6: 自适应修正系数
```c
// 根据传感器状态调整修正系数
float current_index = INDEX;
if (is_turning) {
    current_index = TURN_INDEX;  // 转弯时使用更大的修正系数
}
_iq Steering_Adjustment = _IQmpy(Data_Tracker_Offset, _IQ(current_index));
```

## 5. 实施优先级

### 5.1 高优先级 (立即修复)
1. **修复修正算法**: 恢复 `_IQmpy(Data_Tracker_Offset, _IQ(INDEX))`
2. **移除阻塞延时**: 删除 `Delay(5000)`
3. **修正注释错误**: 更新传感器权重注释

### 5.2 中优先级 (建议改进)
1. **非阻塞转弯逻辑**: 实现状态机式转弯控制
2. **参数优化**: 调整INDEX值和转弯参数
3. **左右转检测**: 增加左转检测逻辑

### 5.3 低优先级 (长期优化)
1. **自适应参数**: 根据转弯状态动态调整参数
2. **转弯完成检测**: 基于传感器状态判断转弯完成
3. **性能监控**: 添加转弯性能统计

## 6. 风险提示

### 6.1 修改风险
- **修正系数调整**: 可能需要多次测试找到最佳值
- **转弯逻辑重构**: 需要充分测试确保稳定性
- **时序问题**: 非阻塞逻辑需要注意时序控制

### 6.2 测试建议
- **渐进式修改**: 先修复高优先级问题，再逐步改进
- **参数调试**: 在实际赛道上测试不同参数组合
- **边界测试**: 测试各种转弯角度和速度条件

## 7. 总结

当前工程的主要问题是**修正算法被过度简化**和**直角弯处理存在阻塞延时**。建议优先修复这两个核心问题，然后再考虑更高级的优化策略。
