******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 07:08:03 2025

OUTPUT FILE NAME:   <TI_CAR1.10.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00004409


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000057d8  0001a828  R  X
  SRAM                  20200000   00008000  00000a99  00007567  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000057d8   000057d8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004c90   00004c90    r-x .text
  00004d50    00004d50    00000a20   00000a20    r-- .rodata
  00005770    00005770    00000068   00000068    r-- .cinit
20200000    20200000    0000089b   00000000    rw-
  20200000    20200000    00000400   00000000    rw- .sysmem
  20200400    20200400    000003aa   00000000    rw- .bss
  202007ac    202007ac    000000ef   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004c90     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000268     Tracker.o (.text.Tracker_Read)
                  00000cf8    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00000f18    000001dc            : _printfi.c.obj (.text._pconv_g)
                  000010f4    000001b0     Task.o (.text.Task_Start)
                  000012a4    00000198     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000143c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000015ce    00000002     Task.o (.text.Task_IdleFunction)
                  000015d0    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00001758    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001894    00000134            : qsort.c.obj (.text.qsort)
                  000019c8    00000130     OLED.o (.text.OLED_ShowChar)
                  00001af8    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001c18    00000118     Task_App.o (.text.Task_Motor_PID)
                  00001d30    00000110     OLED.o (.text.OLED_Init)
                  00001e40    0000010c     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00001f4c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002058    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  0000215c    000000f0     Task_App.o (.text.HD_Init)
                  0000224c    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00002334    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002418    000000e0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  000024f8    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000025d4    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000026ac    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00002784    000000d4     Motor.o (.text.Motor_SetDuty)
                  00002858    000000c4     PID.o (.text.PID_SProsc)
                  0000291c    000000b4     Task.o (.text.Task_Add)
                  000029d0    000000b0     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002a80    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00002b2a    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002b2c    000000a2                            : udivmoddi4.S.obj (.text)
                  00002bce    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002bd0    000000a0     Task_App.o (.text.Task_OLED)
                  00002c70    0000009c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002d0c    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00002da4    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorAFront_init)
                  00002e30    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBFront_init)
                  00002ebc    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002f48    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002fcc    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003048    00000074     Motor.o (.text.Motor_Start)
                  000030bc    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00003130    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  000031a2    0000006e     OLED.o (.text.OLED_ShowString)
                  00003210    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  0000327c    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  000032e6    00000002     --HOLE-- [fill = 0]
                  000032e8    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00003350    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  000033b6    00000002     --HOLE-- [fill = 0]
                  000033b8    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  0000341c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000347e    00000002     --HOLE-- [fill = 0]
                  00003480    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000034e2    00000002     --HOLE-- [fill = 0]
                  000034e4    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00003544    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000035a4    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003602    00000002     --HOLE-- [fill = 0]
                  00003604    0000005c     Task_App.o (.text.Task_Tracker)
                  00003660    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000036bc    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00003718    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003770    00000058            : _printfi.c.obj (.text._pconv_f)
                  000037c8    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000381e    00000002     --HOLE-- [fill = 0]
                  00003820    00000054     Motor.o (.text.CalculateDutyValue)
                  00003874    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000038c8    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  0000391a    00000002     --HOLE-- [fill = 0]
                  0000391c    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  0000396c    00000050     Interrupt.o (.text.Interrupt_Init)
                  000039bc    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00003a0c    0000004c     OLED.o (.text.OLED_Printf)
                  00003a58    0000004c     Task_App.o (.text.Task_Init)
                  00003aa4    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00003aee    00000002     --HOLE-- [fill = 0]
                  00003af0    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00003b3a    0000004a     ADC.o (.text.adc_getValue)
                  00003b84    00000048     Motor.o (.text.Motor_GetSpeed)
                  00003bcc    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003c14    00000048     OLED.o (.text.mspm0_i2c_disable)
                  00003c5c    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00003ca0    00000044     Motor.o (.text.SetPWMValue)
                  00003ce4    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00003d26    00000002     --HOLE-- [fill = 0]
                  00003d28    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00003d6a    00000002     --HOLE-- [fill = 0]
                  00003d6c    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00003dac    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003dec    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00003e2c    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00003e6c    0000003e     Task.o (.text.Task_CMP)
                  00003eaa    00000002     --HOLE-- [fill = 0]
                  00003eac    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003ee8    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003f24    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00003f60    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00003f9c    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00003fd8    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00004014    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00004050    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000408c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000040c6    00000002     --HOLE-- [fill = 0]
                  000040c8    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00004102    00000002     --HOLE-- [fill = 0]
                  00004104    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004138    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000416c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  000041a0    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  000041d4    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00004204    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00004234    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00004264    0000002c     iqmath.a : _IQNmpy.o (.text._IQ24mpy)
                  00004290    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  000042bc    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000042e8    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00004314    0000002a     PID.o (.text.PID_Init)
                  0000433e    00000028     OLED.o (.text.DL_Common_updateReg)
                  00004366    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000438e    00000002     --HOLE-- [fill = 0]
                  00004390    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000043b8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000043e0    00000028     SysTick.o (.text.SysTick_Increasment)
                  00004408    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00004430    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00004456    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  0000447c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  000044a0    00000024                            : muldi3.S.obj (.text.__muldi3)
                  000044c4    00000022     PID.o (.text.PID_SetParams)
                  000044e6    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004508    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00004528    00000020     SysTick.o (.text.Delay)
                  00004548    00000020     main.o (.text.main)
                  00004568    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00004586    00000002     --HOLE-- [fill = 0]
                  00004588    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  000045a4    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  000045c0    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  000045dc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000045f8    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00004614    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00004630    0000001c     Interrupt.o (.text.DL_GPIO_enableInterrupt)
                  0000464c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00004668    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00004684    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  000046a0    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  000046bc    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  000046d8    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  000046f4    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00004710    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  0000472c    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00004748    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00004764    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00004780    0000001c     Task_App.o (.text.TIMA0_IRQHandler)
                  0000479c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000047b4    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000047cc    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  000047e4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000047fc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00004814    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  0000482c    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00004844    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  0000485c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00004874    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  0000488c    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  000048a4    00000018     OLED.o (.text.DL_GPIO_setPins)
                  000048bc    00000018     Tracker.o (.text.DL_GPIO_setPins)
                  000048d4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000048ec    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00004904    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  0000491c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00004934    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  0000494c    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00004964    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  0000497c    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00004994    00000018     OLED.o (.text.DL_I2C_reset)
                  000049ac    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000049c4    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000049dc    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  000049f4    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00004a0c    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00004a24    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00004a3c    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00004a54    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004a6c    00000018     Interrupt.o (.text.DL_Timer_startCounter)
                  00004a84    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00004a9c    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  00004ab4    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  00004aca    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  00004ae0    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00004af6    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00004b0c    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00004b22    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00004b38    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00004b4c    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00004b60    00000014     Tracker.o (.text.DL_GPIO_clearPins)
                  00004b74    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00004b88    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00004b9c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00004bb0    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00004bc4    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00004bd8    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00004bec    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00004c00    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00004c14    00000012     Task_App.o (.text.DL_Timer_getPendingInterrupt)
                  00004c26    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00004c38    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00004c4a    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  00004c5a    00000002     --HOLE-- [fill = 0]
                  00004c5c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00004c6c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00004c7c    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00004c8c    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004c9c    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00004caa    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00004cb8    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00004cc6    00000002     --HOLE-- [fill = 0]
                  00004cc8    0000000c     SysTick.o (.text.Sys_GetTick)
                  00004cd4    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00004cde    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004ce8    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00004cf8    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004d02    0000000a            : vsprintf.c.obj (.text._outc)
                  00004d0c    00000008     Interrupt.o (.text.SysTick_Handler)
                  00004d14    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00004d1c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00004d24    00000006     libc.a : exit.c.obj (.text:abort)
                  00004d2a    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00004d2e    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00004d32    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00004d36    00000002     --HOLE-- [fill = 0]
                  00004d38    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00004d48    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00004d4c    00000004     --HOLE-- [fill = 0]

.cinit     0    00005770    00000068     
                  00005770    0000003f     (.cinit..data.load) [load image, compression = lzss]
                  000057af    00000001     --HOLE-- [fill = 0]
                  000057b0    0000000c     (__TI_handler_table)
                  000057bc    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000057c4    00000010     (__TI_cinit_table)
                  000057d4    00000004     --HOLE-- [fill = 0]

.rodata    0    00004d50    00000a20     
                  00004d50    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00005340    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00005568    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00005570    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00005671    00000041     iqmath.a : _IQNtables.o (.rodata._IQ6div_lookup)
                  000056b2    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000056b4    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000056dc    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  000056f0    00000012     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00005702    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00005713    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00005724    0000000c     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00005730    0000000c     Task_App.o (.rodata.str1.8896853068034818020.1)
                  0000573c    00000008     ti_msp_dl_config.o (.rodata.gMotorAFrontConfig)
                  00005744    00000008     ti_msp_dl_config.o (.rodata.gMotorBFrontConfig)
                  0000574c    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00005754    00000006     Task_App.o (.rodata.str1.11683036942922059812.1)
                  0000575a    00000005     Task_App.o (.rodata.str1.3743034515018940988.1)
                  0000575f    00000003     ti_msp_dl_config.o (.rodata.gMotorAFrontClockConfig)
                  00005762    00000003     ti_msp_dl_config.o (.rodata.gMotorBFrontClockConfig)
                  00005765    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00005768    00000008     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000400     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000003f0     --HOLE--

.bss       0    20200400    000003aa     UNINITIALIZED
                  20200400    000000f0     Task.o (.bss.Task_Schedule)
                  202004f0    000000bc     (.common:gTIMER_0Backup)
                  202005ac    00000004     (.common:ExISR_Flag)
                  202005b0    000000b0     (.common:sensor)
                  20200660    000000a0     (.common:gMotorAFrontBackup)
                  20200700    000000a0     (.common:gMotorBFrontBackup)
                  202007a0    00000007     (.common:tick)
                  202007a7    00000001     (.common:Cycle)
                  202007a8    00000001     (.common:HD_count)
                  202007a9    00000001     (.common:set_Cycle)

.data      0    202007ac    000000ef     UNINITIALIZED
                  202007ac    00000048     Motor.o (.data.Motor_Font_Left)
                  202007f4    00000048     Motor.o (.data.Motor_Font_Right)
                  2020083c    00000010     Task_App.o (.data.Analog)
                  2020084c    00000010     Task_App.o (.data.black)
                  2020085c    00000010     Task_App.o (.data.white)
                  2020086c    00000008     Task_App.o (.data.Motor)
                  20200874    00000007     Task_App.o (.data.Data_Tracker_Input)
                  2020087b    00000001     Tracker.o (.data.Enable_Flag)
                  2020087c    00000004     Task_App.o (.data.Data_MotorEncoder)
                  20200880    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  20200884    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  20200888    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  2020088c    00000004     SysTick.o (.data.delayTick)
                  20200890    00000004     Tracker.o (.data.turn_start_time)
                  20200894    00000004     SysTick.o (.data.uwTick)
                  20200898    00000001     Task_App.o (.data.Motor_Flag)
                  20200899    00000001     Task.o (.data.Task_Num)
                  2020089a    00000001     Tracker.o (.data.is_turning)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2710    95        508    
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2750    287       509    
                                                                 
    .\APP\Src\
       Task_App.o                       894     61        254    
       Interrupt.o                      462     0         4      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1356    61        258    
                                                                 
    .\BSP\Src\
       OLED_Font.o                      0       2072      0      
       OLED.o                           1858    0         0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1178    0         0      
       Task.o                           676     0         241    
       Motor.o                          576     0         144    
       Tracker.o                        660     0         13     
       PID.o                            272     0         0      
       ADC.o                            238     0         0      
       SysTick.o                        84      0         8      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5542    2072      406    
                                                                 
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_i2c.o                         132     0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1182    0         0      
                                                                 
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/rts/mspm0g1x0x_g3x0x/iqmath.a
       _IQNdiv.o                        268     0         0      
       _IQNtables.o                     0       65        0      
       _IQNtoF.o                        48      0         0      
       _IQNmpy.o                        44      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           360     65        0      
                                                                 
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       vsprintf.c.obj                   78      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5736    291       4      
                                                                 
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     418     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       comparesf2.S.obj                 118     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2630    0         0      
                                                                 
       Heap:                            0       0         1024   
       Stack:                           0       0         512    
       Linker Generated:                0       99        0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     19560   2875      2713   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000057c4 records: 2, size/record: 8, table size: 16
	.data: load addr=00005770, load size=0000003f bytes, run addr=202007ac, run size=000000ef bytes, compression=lzss
	.bss: load addr=000057bc, load size=00000008 bytes, run addr=20200400, run size=000003aa bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000057b0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   0000143d     00004ce8     00004ce6   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00004409     00004d38     00004d32   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00004d2b  ADC0_IRQHandler                      
00004d2b  ADC1_IRQHandler                      
00004d2b  AES_IRQHandler                       
2020083c  Analog                               
00004d2e  C$$EXIT                              
00004d2b  CANFD0_IRQHandler                    
202007a7  Cycle                                
00004d2b  DAC0_IRQHandler                      
00003d6d  DL_ADC12_setClockConfig              
00004cd5  DL_Common_delayCycles                
000035a5  DL_I2C_fillControllerTXFIFO          
00004457  DL_I2C_setClockConfig                
000024f9  DL_SYSCTL_configSYSPLL               
000033b9  DL_SYSCTL_setHFCLKSourceHFXTParams   
00003c5d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002059  DL_Timer_initFourCCPWMMode           
0000224d  DL_Timer_initTimerMode               
00004749  DL_Timer_setCaptCompUpdateMethod     
00004a55  DL_Timer_setCaptureCompareOutCtl     
00004c6d  DL_Timer_setCaptureCompareValue      
00004765  DL_Timer_setClockConfig              
00004d2b  DMA_IRQHandler                       
2020087c  Data_MotorEncoder                    
20200880  Data_Motor_TarSpeed                  
20200874  Data_Tracker_Input                   
20200884  Data_Tracker_Offset                  
00004d2b  Default_Handler                      
00004529  Delay                                
202005ac  ExISR_Flag                           
00004d2b  GROUP0_IRQHandler                    
000029d1  GROUP1_IRQHandler                    
00002419  Get_Analog_value                     
00003f61  Get_Anolog_Value                     
0000215d  HD_Init                              
202007a8  HD_count                             
00004d2f  HOSTexit                             
00004d2b  HardFault_Handler                    
00004d2b  I2C0_IRQHandler                      
00004d2b  I2C1_IRQHandler                      
0000327d  I2C_OLED_Clear                       
00003f9d  I2C_OLED_Set_Pos                     
00002d0d  I2C_OLED_WR_Byte                     
000034e5  I2C_OLED_i2c_sda_unlock              
0000396d  Interrupt_Init                       
2020086c  Motor                                
20200898  Motor_Flag                           
202007ac  Motor_Font_Left                      
202007f4  Motor_Font_Right                     
00003b85  Motor_GetSpeed                       
00002785  Motor_SetDuty                        
00003049  Motor_Start                          
00004d2b  NMI_Handler                          
000015d1  No_MCU_Ganv_Sensor_Init              
00003131  No_MCU_Ganv_Sensor_Init_Frist        
00003ce5  No_Mcu_Ganv_Sensor_Task_Without_tick 
00001d31  OLED_Init                            
00003a0d  OLED_Printf                          
000019c9  OLED_ShowChar                        
000031a3  OLED_ShowString                      
00004315  PID_Init                             
00002859  PID_SProsc                           
000044c5  PID_SetParams                        
00004d2b  PendSV_Handler                       
00004d2b  RTC_IRQHandler                       
00004d33  Reset_Handler                        
00004d2b  SPI0_IRQHandler                      
00004d2b  SPI1_IRQHandler                      
00004d2b  SVC_Handler                          
0000416d  SYSCFG_DL_ADC1_init                  
000012a5  SYSCFG_DL_GPIO_init                  
00003545  SYSCFG_DL_I2C_OLED_init              
00002da5  SYSCFG_DL_MotorAFront_init           
00002e31  SYSCFG_DL_MotorBFront_init           
00003875  SYSCFG_DL_SYSCTL_init                
00004c7d  SYSCFG_DL_SYSTICK_init               
000041a1  SYSCFG_DL_TIMER_0_init               
00003bcd  SYSCFG_DL_init                       
00002c71  SYSCFG_DL_initPower                  
00004d0d  SysTick_Handler                      
000043e1  SysTick_Increasment                  
00004cc9  Sys_GetTick                          
00004781  TIMA0_IRQHandler                     
00004d2b  TIMA1_IRQHandler                     
00004d2b  TIMG0_IRQHandler                     
00004d2b  TIMG12_IRQHandler                    
00004d2b  TIMG6_IRQHandler                     
00004d2b  TIMG7_IRQHandler                     
00004d2b  TIMG8_IRQHandler                     
00004c27  TI_memcpy_small                      
00004cb9  TI_memset_small                      
0000291d  Task_Add                             
000015cf  Task_IdleFunction                    
00003a59  Task_Init                            
00001c19  Task_Motor_PID                       
00002bd1  Task_OLED                            
000010f5  Task_Start                           
00003605  Task_Tracker                         
00000a91  Tracker_Read                         
00004d2b  UART0_IRQHandler                     
00004d2b  UART1_IRQHandler                     
00004d2b  UART2_IRQHandler                     
00004d2b  UART3_IRQHandler                     
00001e41  _IQ24div                             
00004265  _IQ24mpy                             
00004205  _IQ24toF                             
00005671  _IQ6div_lookup                       
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000057c4  __TI_CINIT_Base                      
000057d4  __TI_CINIT_Limit                     
000057d4  __TI_CINIT_Warm                      
000057b0  __TI_Handler_Table_Base              
000057bc  __TI_Handler_Table_Limit             
00004051  __TI_auto_init_nobinit_nopinit       
00002fcd  __TI_decompress_lzss                 
00004c39  __TI_decompress_none                 
00003719  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00004b23  __TI_zero_init_nomemset              
00001447  __adddf3                             
000026b7  __addsf3                             
00005570  __aeabi_ctype_table_                 
00005570  __aeabi_ctype_table_C                
00003af1  __aeabi_d2iz                         
00003d29  __aeabi_d2uiz                        
00001447  __aeabi_dadd                         
0000341d  __aeabi_dcmpeq                       
00003459  __aeabi_dcmpge                       
0000346d  __aeabi_dcmpgt                       
00003445  __aeabi_dcmple                       
00003431  __aeabi_dcmplt                       
00001f4d  __aeabi_ddiv                         
00002335  __aeabi_dmul                         
0000143d  __aeabi_dsub                         
20200888  __aeabi_errno                        
00004d15  __aeabi_errno_addr                   
00003ded  __aeabi_f2d                          
000026b7  __aeabi_fadd                         
00003481  __aeabi_fcmpeq                       
000034bd  __aeabi_fcmpge                       
000034d1  __aeabi_fcmpgt                       
000034a9  __aeabi_fcmple                       
00003495  __aeabi_fcmplt                       
00002ebd  __aeabi_fmul                         
000026ad  __aeabi_fsub                         
000042bd  __aeabi_i2d                          
00003fd9  __aeabi_i2f                          
000037c9  __aeabi_idiv                         
00002b2b  __aeabi_idiv0                        
000037c9  __aeabi_idivmod                      
00002bcf  __aeabi_ldiv0                        
00004569  __aeabi_llsl                         
000044a1  __aeabi_lmul                         
00004d1d  __aeabi_memcpy                       
00004d1d  __aeabi_memcpy4                      
00004d1d  __aeabi_memcpy8                      
00004c9d  __aeabi_memset                       
00004c9d  __aeabi_memset4                      
00004c9d  __aeabi_memset8                      
0000447d  __aeabi_ui2d                         
00003dad  __aeabi_uidiv                        
00003dad  __aeabi_uidivmod                     
00004bed  __aeabi_uldivmod                     
00004569  __ashldi3                            
ffffffff  __binit__                            
000032e9  __cmpdf2                             
0000408d  __cmpsf2                             
00001f4d  __divdf3                             
000032e9  __eqdf2                              
0000408d  __eqsf2                              
00003ded  __extendsfdf2                        
00003af1  __fixdfsi                            
00003d29  __fixunsdfsi                         
000042bd  __floatsidf                          
00003fd9  __floatsisf                          
0000447d  __floatunsidf                        
000030bd  __gedf2                              
00004015  __gesf2                              
000030bd  __gtdf2                              
00004015  __gtsf2                              
000032e9  __ledf2                              
0000408d  __lesf2                              
000032e9  __ltdf2                              
0000408d  __ltsf2                              
UNDEFED   __mpu_init                           
00002335  __muldf3                             
000044a1  __muldi3                             
000040c9  __muldsi3                            
00002ebd  __mulsf3                             
000032e9  __nedf2                              
0000408d  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
0000143d  __subdf3                             
000026ad  __subsf3                             
00002b2d  __udivmoddi4                         
00004409  _c_int00_noargs                      
20200000  _sys_memory                          
UNDEFED   _system_post_cinit                   
00004d49  _system_pre_init                     
00004d25  abort                                
00003b3b  adc_getValue                         
00005340  asc2_0806                            
00004d50  asc2_1608                            
00003e2d  atoi                                 
ffffffff  binit                                
2020084c  black                                
00003211  convertAnalogToDigital               
2020088c  delayTick                            
00003661  frexp                                
00003661  frexpl                               
20200660  gMotorAFrontBackup                   
20200700  gMotorBFrontBackup                   
202004f0  gTIMER_0Backup                       
00000000  interruptVectors                     
2020089a  is_turning                           
000025d5  ldexp                                
000025d5  ldexpl                               
00004549  main                                 
000044e7  memccpy                              
00002a81  normalizeAnalogValues                
00001895  qsort                                
000025d5  scalbn                               
000025d5  scalbnl                              
202005b0  sensor                               
202007a9  set_Cycle                            
202007a0  tick                                 
20200894  uwTick                               
000042e9  vsprintf                             
00004c8d  wcslen                               
2020085c  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000a91  Tracker_Read                         
000010f5  Task_Start                           
000012a5  SYSCFG_DL_GPIO_init                  
0000143d  __aeabi_dsub                         
0000143d  __subdf3                             
00001447  __adddf3                             
00001447  __aeabi_dadd                         
000015cf  Task_IdleFunction                    
000015d1  No_MCU_Ganv_Sensor_Init              
00001895  qsort                                
000019c9  OLED_ShowChar                        
00001c19  Task_Motor_PID                       
00001d31  OLED_Init                            
00001e41  _IQ24div                             
00001f4d  __aeabi_ddiv                         
00001f4d  __divdf3                             
00002059  DL_Timer_initFourCCPWMMode           
0000215d  HD_Init                              
0000224d  DL_Timer_initTimerMode               
00002335  __aeabi_dmul                         
00002335  __muldf3                             
00002419  Get_Analog_value                     
000024f9  DL_SYSCTL_configSYSPLL               
000025d5  ldexp                                
000025d5  ldexpl                               
000025d5  scalbn                               
000025d5  scalbnl                              
000026ad  __aeabi_fsub                         
000026ad  __subsf3                             
000026b7  __addsf3                             
000026b7  __aeabi_fadd                         
00002785  Motor_SetDuty                        
00002859  PID_SProsc                           
0000291d  Task_Add                             
000029d1  GROUP1_IRQHandler                    
00002a81  normalizeAnalogValues                
00002b2b  __aeabi_idiv0                        
00002b2d  __udivmoddi4                         
00002bcf  __aeabi_ldiv0                        
00002bd1  Task_OLED                            
00002c71  SYSCFG_DL_initPower                  
00002d0d  I2C_OLED_WR_Byte                     
00002da5  SYSCFG_DL_MotorAFront_init           
00002e31  SYSCFG_DL_MotorBFront_init           
00002ebd  __aeabi_fmul                         
00002ebd  __mulsf3                             
00002fcd  __TI_decompress_lzss                 
00003049  Motor_Start                          
000030bd  __gedf2                              
000030bd  __gtdf2                              
00003131  No_MCU_Ganv_Sensor_Init_Frist        
000031a3  OLED_ShowString                      
00003211  convertAnalogToDigital               
0000327d  I2C_OLED_Clear                       
000032e9  __cmpdf2                             
000032e9  __eqdf2                              
000032e9  __ledf2                              
000032e9  __ltdf2                              
000032e9  __nedf2                              
000033b9  DL_SYSCTL_setHFCLKSourceHFXTParams   
0000341d  __aeabi_dcmpeq                       
00003431  __aeabi_dcmplt                       
00003445  __aeabi_dcmple                       
00003459  __aeabi_dcmpge                       
0000346d  __aeabi_dcmpgt                       
00003481  __aeabi_fcmpeq                       
00003495  __aeabi_fcmplt                       
000034a9  __aeabi_fcmple                       
000034bd  __aeabi_fcmpge                       
000034d1  __aeabi_fcmpgt                       
000034e5  I2C_OLED_i2c_sda_unlock              
00003545  SYSCFG_DL_I2C_OLED_init              
000035a5  DL_I2C_fillControllerTXFIFO          
00003605  Task_Tracker                         
00003661  frexp                                
00003661  frexpl                               
00003719  __TI_ltoa                            
000037c9  __aeabi_idiv                         
000037c9  __aeabi_idivmod                      
00003875  SYSCFG_DL_SYSCTL_init                
0000396d  Interrupt_Init                       
00003a0d  OLED_Printf                          
00003a59  Task_Init                            
00003af1  __aeabi_d2iz                         
00003af1  __fixdfsi                            
00003b3b  adc_getValue                         
00003b85  Motor_GetSpeed                       
00003bcd  SYSCFG_DL_init                       
00003c5d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003ce5  No_Mcu_Ganv_Sensor_Task_Without_tick 
00003d29  __aeabi_d2uiz                        
00003d29  __fixunsdfsi                         
00003d6d  DL_ADC12_setClockConfig              
00003dad  __aeabi_uidiv                        
00003dad  __aeabi_uidivmod                     
00003ded  __aeabi_f2d                          
00003ded  __extendsfdf2                        
00003e2d  atoi                                 
00003f61  Get_Anolog_Value                     
00003f9d  I2C_OLED_Set_Pos                     
00003fd9  __aeabi_i2f                          
00003fd9  __floatsisf                          
00004015  __gesf2                              
00004015  __gtsf2                              
00004051  __TI_auto_init_nobinit_nopinit       
0000408d  __cmpsf2                             
0000408d  __eqsf2                              
0000408d  __lesf2                              
0000408d  __ltsf2                              
0000408d  __nesf2                              
000040c9  __muldsi3                            
0000416d  SYSCFG_DL_ADC1_init                  
000041a1  SYSCFG_DL_TIMER_0_init               
00004205  _IQ24toF                             
00004265  _IQ24mpy                             
000042bd  __aeabi_i2d                          
000042bd  __floatsidf                          
000042e9  vsprintf                             
00004315  PID_Init                             
000043e1  SysTick_Increasment                  
00004409  _c_int00_noargs                      
00004457  DL_I2C_setClockConfig                
0000447d  __aeabi_ui2d                         
0000447d  __floatunsidf                        
000044a1  __aeabi_lmul                         
000044a1  __muldi3                             
000044c5  PID_SetParams                        
000044e7  memccpy                              
00004529  Delay                                
00004549  main                                 
00004569  __aeabi_llsl                         
00004569  __ashldi3                            
00004749  DL_Timer_setCaptCompUpdateMethod     
00004765  DL_Timer_setClockConfig              
00004781  TIMA0_IRQHandler                     
00004a55  DL_Timer_setCaptureCompareOutCtl     
00004b23  __TI_zero_init_nomemset              
00004bed  __aeabi_uldivmod                     
00004c27  TI_memcpy_small                      
00004c39  __TI_decompress_none                 
00004c6d  DL_Timer_setCaptureCompareValue      
00004c7d  SYSCFG_DL_SYSTICK_init               
00004c8d  wcslen                               
00004c9d  __aeabi_memset                       
00004c9d  __aeabi_memset4                      
00004c9d  __aeabi_memset8                      
00004cb9  TI_memset_small                      
00004cc9  Sys_GetTick                          
00004cd5  DL_Common_delayCycles                
00004d0d  SysTick_Handler                      
00004d15  __aeabi_errno_addr                   
00004d1d  __aeabi_memcpy                       
00004d1d  __aeabi_memcpy4                      
00004d1d  __aeabi_memcpy8                      
00004d25  abort                                
00004d2b  ADC0_IRQHandler                      
00004d2b  ADC1_IRQHandler                      
00004d2b  AES_IRQHandler                       
00004d2b  CANFD0_IRQHandler                    
00004d2b  DAC0_IRQHandler                      
00004d2b  DMA_IRQHandler                       
00004d2b  Default_Handler                      
00004d2b  GROUP0_IRQHandler                    
00004d2b  HardFault_Handler                    
00004d2b  I2C0_IRQHandler                      
00004d2b  I2C1_IRQHandler                      
00004d2b  NMI_Handler                          
00004d2b  PendSV_Handler                       
00004d2b  RTC_IRQHandler                       
00004d2b  SPI0_IRQHandler                      
00004d2b  SPI1_IRQHandler                      
00004d2b  SVC_Handler                          
00004d2b  TIMA1_IRQHandler                     
00004d2b  TIMG0_IRQHandler                     
00004d2b  TIMG12_IRQHandler                    
00004d2b  TIMG6_IRQHandler                     
00004d2b  TIMG7_IRQHandler                     
00004d2b  TIMG8_IRQHandler                     
00004d2b  UART0_IRQHandler                     
00004d2b  UART1_IRQHandler                     
00004d2b  UART2_IRQHandler                     
00004d2b  UART3_IRQHandler                     
00004d2e  C$$EXIT                              
00004d2f  HOSTexit                             
00004d33  Reset_Handler                        
00004d49  _system_pre_init                     
00004d50  asc2_1608                            
00005340  asc2_0806                            
00005570  __aeabi_ctype_table_                 
00005570  __aeabi_ctype_table_C                
00005671  _IQ6div_lookup                       
000057b0  __TI_Handler_Table_Base              
000057bc  __TI_Handler_Table_Limit             
000057c4  __TI_CINIT_Base                      
000057d4  __TI_CINIT_Limit                     
000057d4  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  _sys_memory                          
202004f0  gTIMER_0Backup                       
202005ac  ExISR_Flag                           
202005b0  sensor                               
20200660  gMotorAFrontBackup                   
20200700  gMotorBFrontBackup                   
202007a0  tick                                 
202007a7  Cycle                                
202007a8  HD_count                             
202007a9  set_Cycle                            
202007ac  Motor_Font_Left                      
202007f4  Motor_Font_Right                     
2020083c  Analog                               
2020084c  black                                
2020085c  white                                
2020086c  Motor                                
20200874  Data_Tracker_Input                   
2020087c  Data_MotorEncoder                    
20200880  Data_Motor_TarSpeed                  
20200884  Data_Tracker_Offset                  
20200888  __aeabi_errno                        
2020088c  delayTick                            
20200894  uwTick                               
20200898  Motor_Flag                           
2020089a  is_turning                           
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[248 symbols]
