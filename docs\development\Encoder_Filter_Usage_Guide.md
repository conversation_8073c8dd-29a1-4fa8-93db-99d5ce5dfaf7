# 编码器滤波系统完整使用指南

## 📋 **系统概述**

编码器滤波系统是一个多层次的信号处理解决方案，专门用于消除编码器信号中的尖峰、噪声和干扰。

### **核心特性**
- ✅ **多重滤波算法**：移动平均、中值滤波、卡尔曼滤波
- ✅ **智能防抖**：时间窗口防抖，防止机械抖动
- ✅ **异常检测**：自动检测和过滤异常尖峰
- ✅ **动态配置**：运行时调整滤波参数
- ✅ **调试监控**：完整的调试和性能监控

## 🔧 **快速集成步骤**

### **步骤1：添加头文件包含**

在 `BSP/Inc/SysConfig.h` 中添加：
```c
/*BSP*/
#include "PID.h"
#include "Task.h"
#include "Key_Led.h"
#include "SysTick.h"
#include "Motor.h"
#include "Serial.h"
#include "Tracker.h"
#include "OLED.h"
#include "Camera.h"
#include "Encoder_Filter.h"  // 【新增】编码器滤波系统
```

### **步骤2：替换中断处理函数**

在 `APP/Src/Interrupt.c` 中：
```c
// 【注释掉原有的GROUP1_IRQHandler函数】
/*
void GROUP1_IRQHandler(void)
{
    // 原有代码...
}
*/

// 【新增】包含集成的中断处理
#include "Interrupt_Filter_Integration.c"
```

### **步骤3：初始化滤波系统**

在 `main.c` 或 `Task_Init()` 中添加：
```c
int main(void)
{
    SYSCFG_DL_init();    
    Task_Init();
    
    // 【新增】初始化编码器滤波系统
    Encoder_Filter_System_Init();
    
    while (1)
    {
        Task_Start(Sys_GetTick);
    }
}
```

## ⚙️ **滤波算法详解**

### **1. 移动平均滤波器**

**原理**：对最近N个采样值求平均
```c
// 数学公式
filtered_value = (x[n] + x[n-1] + ... + x[n-N+1]) / N

// 优化实现（避免重复计算）
sum = sum - old_value + new_value;
filtered_value = sum / N;
```

**特点**：
- ✅ 计算简单，实时性好
- ✅ 能有效平滑高频噪声
- ❌ 对突变信号有延迟

**适用场景**：连续运行的电机编码器

### **2. 中值滤波器**

**原理**：取最近N个采样值的中位数
```c
// 算法步骤
1. 收集N个采样值：[x1, x2, x3, x4, x5]
2. 排序：[x_min, ..., x_median, ..., x_max]
3. 输出中位数：x_median
```

**特点**：
- ✅ 能完全消除脉冲干扰
- ✅ 保持信号边沿特性
- ❌ 计算复杂度较高

**适用场景**：存在脉冲干扰的环境

### **3. 卡尔曼滤波器**

**原理**：基于状态估计的最优滤波
```c
// 预测步骤
x_pred = x_prev;           // 状态预测
P_pred = P_prev + Q;       // 误差协方差预测

// 更新步骤
K = P_pred / (P_pred + R); // 卡尔曼增益
x = x_pred + K * (z - x_pred); // 状态更新
P = (1 - K) * P_pred;      // 误差协方差更新
```

**参数说明**：
- `Q`：过程噪声协方差（系统不确定性）
- `R`：测量噪声协方差（传感器噪声）
- `K`：卡尔曼增益（信任度权重）

**特点**：
- ✅ 理论最优滤波效果
- ✅ 能处理系统和测量噪声
- ❌ 需要调整参数

## 📊 **滤波效果对比**

| 滤波算法 | 计算复杂度 | 延迟 | 脉冲抑制 | 噪声抑制 | 适用场景 |
|---------|-----------|------|----------|----------|----------|
| 移动平均 | 低 | 中等 | 中等 | 好 | 通用场景 |
| 中值滤波 | 高 | 低 | 优秀 | 中等 | 脉冲干扰 |
| 卡尔曼滤波 | 中等 | 低 | 好 | 优秀 | 复杂噪声 |

## 🎛️ **参数配置指南**

### **基础配置**
```c
// 在 BSP/Inc/Encoder_Filter.h 中调整

// 移动平均滤波器
#define ENCODER_FILTER_SIZE 5              // 窗口大小：3-10
#define ENCODER_MAX_CHANGE_PER_INTERRUPT 3 // 变化限制：1-5

// 防抖参数
#define ENCODER_DEBOUNCE_TIME_US 50        // 防抖时间：20-100微秒

// 异常检测
#define ENCODER_SPIKE_THRESHOLD 20         // 尖峰阈值：10-50
```

### **高级配置**
```c
// 卡尔曼滤波器参数
#define KALMAN_Q 0.1f    // 过程噪声：0.01-1.0
#define KALMAN_R 1.0f    // 测量噪声：0.1-10.0

// 中值滤波器
#define MEDIAN_FILTER_SIZE 5  // 窗口大小：3-7（奇数）
```

## 🔍 **调试和监控**

### **实时状态监控**
```c
void Task_Monitor_Encoder_Filter(void)
{
    static uint32_t last_print_time = 0;
    uint32_t current_time = Sys_GetTick();
    
    // 每1秒打印一次状态
    if (current_time - last_print_time > 1000) {
        Encoder_Filter_PrintStatus();
        last_print_time = current_time;
    }
}
```

### **性能测试**
```c
void Test_Encoder_Filter_Performance(void)
{
    uint8_t test_result = Encoder_Filter_PerformanceTest();
    if (test_result == 0) {
        printf("编码器滤波器测试通过\n");
    } else {
        printf("编码器滤波器测试失败\n");
    }
}
```

### **调试信息解读**
```c
Encoder_Filter_Debug_t debug_info;
Encoder_Filter_GetDebugInfo(&encoder_filter_left, &debug_info);

printf("总样本数: %lu\n", debug_info.total_samples);
printf("尖峰滤除: %lu (%.1f%%)\n", 
       debug_info.spike_filtered, 
       (float)debug_info.spike_filtered * 100.0f / debug_info.total_samples);
printf("防抖拒绝: %lu (%.1f%%)\n", 
       debug_info.debounce_rejected,
       (float)debug_info.debounce_rejected * 100.0f / debug_info.total_samples);
```

## 🎯 **使用场景和配置建议**

### **场景1：正常运行环境**
```c
// 推荐配置：只启用移动平均
Encoder_Filter_SetConfig(&encoder_filter_left, true, false, false);
```

### **场景2：高干扰环境**
```c
// 推荐配置：启用移动平均+中值滤波
Encoder_Filter_SetConfig(&encoder_filter_left, true, true, false);
```

### **场景3：极端噪声环境**
```c
// 推荐配置：启用所有滤波器
Encoder_Filter_SetConfig(&encoder_filter_left, true, true, true);
```

### **场景4：高精度要求**
```c
// 推荐配置：只启用卡尔曼滤波
Encoder_Filter_SetConfig(&encoder_filter_left, false, false, true);
```

## 🚀 **性能优化建议**

### **1. 内存优化**
- 根据实际需要调整滤波器窗口大小
- 不使用的滤波算法可以关闭以节省内存

### **2. 计算优化**
- 移动平均使用累计和避免重复计算
- 中值滤波可以考虑使用插入排序优化

### **3. 实时性优化**
- 中断处理函数中只做必要的计算
- 复杂的滤波计算可以放在主循环中

## ⚠️ **注意事项**

### **1. 参数调整**
- 滤波器窗口越大，平滑效果越好，但延迟也越大
- 防抖时间不宜过长，否则会丢失有效信号

### **2. 系统集成**
- 确保在系统初始化时调用滤波器初始化函数
- 注意中断优先级配置，避免冲突

### **3. 调试验证**
- 使用示波器或调试器观察滤波前后的波形
- 通过调试信息监控滤波器工作状态

## 📈 **预期效果**

### **滤波前**
- 编码器读数出现32+的异常尖峰
- 波形不稳定，影响PID控制精度

### **滤波后**
- 编码器读数平滑稳定
- 异常尖峰被有效滤除
- PID控制精度显著提升

## 🔧 **故障排除**

### **问题1：滤波效果不明显**
- 检查滤波器是否正确初始化
- 调整滤波参数（增大窗口大小或启用更多滤波器）

### **问题2：响应延迟过大**
- 减小滤波器窗口大小
- 关闭不必要的滤波算法

### **问题3：编译错误**
- 检查头文件包含路径
- 确保所有源文件都已添加到工程中
