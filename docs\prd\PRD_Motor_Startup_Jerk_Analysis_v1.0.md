# PRD - 电机上电抽动问题分析

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-07-31 |
| 负责人 | Emma (产品经理) |
| 项目名称 | TI_CAR1.4 电机上电抽动问题诊断 |
| 文档状态 | 需求分析完成 |

## 2. 背景与问题陈述

### 2.1 问题描述
在TI_CAR1.4智能小车项目中，发现上电时`Motor_Font_Left.Motor_PID_Instance.Out`不为0，导致电机会产生一个短暂的抽动现象，影响系统启动的平稳性。

### 2.2 问题现象
- **症状**: 上电瞬间电机会"抽一下"
- **根本原因**: `Motor_PID_Instance.Out`初始值不为0
- **影响范围**: 主要影响左前轮电机，可能右前轮也存在同样问题
- **发生时机**: 系统上电初始化阶段

### 2.3 当前初始化流程分析

#### 2.3.1 系统启动顺序
```c
main() 
├── SYSCFG_DL_init()     // 硬件配置初始化
├── Task_Init()          // 任务系统初始化
│   ├── Motor_Start()    // 电机启动 ⚠️ 关键点
│   ├── OLED_Init()      // OLED初始化
│   ├── Interrupt_Init() // 中断初始化
│   └── Task_Add(...)    // 添加任务
└── Task_Start()         // 开始任务调度
```

#### 2.3.2 电机初始化流程
```c
Motor_Start() 
├── DL_TimerG_startCounter()     // 启动PWM定时器
├── Motor_SetDuty(..., 0.0f)     // 设置PWM为0 ✅ 正确
├── PID_Init()                   // PID初始化 ⚠️ 关键点
└── PID_SetParams()              // 设置PID参数
```

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **消除启动抽动**: 确保电机上电时完全静止
2. **保证初始化安全**: PID输出值在使用前必须为0
3. **提升用户体验**: 系统启动平稳无异常

### 3.2 关键结果 (Key Results)
1. **PID输出初始值**: `Motor_PID_Instance.Out` 上电时必须为0
2. **电机静止时间**: 上电后至少500ms内电机保持静止
3. **初始化顺序**: PWM设置必须在PID计算之前完成
4. **系统稳定性**: 连续100次上电测试无异常

### 3.3 反向指标 (Counter Metrics)
1. **启动延迟**: 初始化优化不应增加启动时间超过50ms
2. **内存占用**: 修复方案不应增加内存占用
3. **代码复杂度**: 保持代码简洁性

## 4. 用户画像与用户故事

### 4.1 目标用户
- **终端用户**: 期望小车启动平稳，无异常抖动
- **开发人员**: 需要可靠的电机控制系统
- **测试人员**: 需要一致的系统行为进行测试

### 4.2 用户故事
- **作为终端用户**, 我希望小车上电时平稳启动，这样我就能确信系统工作正常
- **作为开发人员**, 我希望PID系统初始化可靠，这样我就能专注于算法优化
- **作为测试人员**, 我希望每次上电行为一致，这样我就能进行可重复的测试

## 5. 功能规格详述

### 5.1 电机启动行为标准

#### 5.1.1 理想启动行为
1. **上电阶段**: 电机完全静止，无任何转动
2. **初始化阶段**: PWM输出为0，PID输出为0
3. **就绪阶段**: 系统准备好接收控制指令
4. **运行阶段**: 根据目标值正常运行

#### 5.1.2 PID初始化标准
- **所有PID变量必须清零**:
  - `Out = 0` (输出值)
  - `Target = 0` (目标值)
  - `Acutal_Now = 0` (当前值)
  - `Acutal_Last = 0` (上次值)
  - `Err_Now = 0` (当前误差)
  - `Err_Last = 0` (上次误差)
  - `Err_Int = 0` (积分误差)
  - `Dif_Out = 0` (微分输出)

#### 5.1.3 初始化时序要求
1. **硬件初始化** → **PWM设置为0** → **PID初始化** → **参数设置**
2. **在第一次PID计算前，确保所有变量已正确初始化**
3. **PWM输出必须在PID计算之前设置为安全值**

### 5.2 问题根因分析

#### 5.2.1 可能的原因
1. **未初始化内存**: 全局变量可能包含随机值
2. **初始化顺序错误**: PID计算在初始化之前执行
3. **结构体初始化不完整**: 部分成员未正确清零
4. **编译器优化问题**: 某些初始化被优化掉

#### 5.2.2 当前代码分析

**🔍 Motor结构体定义**:
```c
// Motor.c - 全局变量定义
MOTOR_Def_t Motor_Font_Left = {
    .Motor_Dirc = DIRC_FOWARD,
    .Motor_Encoder_Addr = &Data_MotorEncoder[0],
    .Motor_PWM_TIMX = MotorAFront_INST,
    // ... 其他成员
    // ⚠️ 注意：Motor_PID_Instance 未显式初始化！
};
```

**🔍 PID_Init函数**:
```c
void PID_Init(PID_Def_t *pid)
{
    // ⚠️ 注意：Kp, Ki, Kd 被注释掉了！
    // pid->Kp = 0;
    // pid->Ki = 0;
    // pid->Kd = 0;
    pid->Acutal_Now = 0;
    pid->Acutal_Last = 0;
    pid->Out = 0;           // ✅ 这里设置为0
    pid->Target = 0;
    // ... 其他成员清零
}
```

#### 5.2.3 潜在问题点
1. **全局结构体初始化**: `Motor_Font_Left`中的`Motor_PID_Instance`可能未完全清零
2. **初始化时机**: `Motor_Start()`在任务开始前调用，但可能存在时序问题
3. **内存布局**: 编译器可能将结构体放在未清零的内存区域

## 6. 解决方案建议

### 6.1 方案A: 显式结构体初始化
```c
// 在Motor.c中显式初始化PID结构体
MOTOR_Def_t Motor_Font_Left = {
    .Motor_Dirc = DIRC_FOWARD,
    .Motor_Encoder_Addr = &Data_MotorEncoder[0],
    .Motor_PWM_TIMX = MotorAFront_INST,
    .Motor_PWM_CH1 = DL_TIMER_CC_0_INDEX,
    .Motor_PWM_CH2 = DL_TIMER_CC_1_INDEX,
    .Motor_PWM_Period = 100,
    .Is_TimerG = true,
    .Motor_PID_Instance = {0}  // ✅ 显式清零
};
```

### 6.2 方案B: 增强PID_Init函数
```c
void PID_Init(PID_Def_t *pid)
{
    // 使用memset确保完全清零
    memset(pid, 0, sizeof(PID_Def_t));
    
    // 或者显式初始化所有成员
    pid->Kp = 0.0f;
    pid->Ki = 0.0f;
    pid->Kd = 0.0f;
    pid->Acutal_Now = 0.0f;
    pid->Acutal_Last = 0.0f;
    pid->Target = 0.0f;
    pid->Out = 0.0f;
    pid->Dif_Out = 0.0f;
    pid->Err_Now = 0.0f;
    pid->Err_Last = 0.0f;
    pid->Err_Int = 0.0f;
}
```

### 6.3 方案C: 调整初始化顺序
```c
void Motor_Start(void)
{
    // 1. 首先初始化PID（确保输出为0）
    PID_Init(&Motor_Font_Left.Motor_PID_Instance);
    PID_Init(&Motor_Font_Right.Motor_PID_Instance);
    
    // 2. 启动PWM定时器
    DL_TimerG_startCounter(MotorAFront_INST);
    DL_TimerG_startCounter(MotorBFront_INST);
    
    // 3. 设置PWM为0（双重保险）
    Motor_SetDuty(&Motor_Font_Left, 0.0f);
    Motor_SetDuty(&Motor_Font_Right, 0.0f);
    
    // 4. 最后设置PID参数
    PID_SetParams(&Motor_Font_Left.Motor_PID_Instance, 8.5f, 1.1f, 0.5f);
    PID_SetParams(&Motor_Font_Right.Motor_PID_Instance, 8.5f, 1.1f, 0.5f);
}
```

## 7. 推荐实施方案

### 7.1 综合解决方案
**建议采用方案A + 方案B + 方案C的组合**:

1. **显式初始化结构体** (方案A)
2. **增强PID_Init函数** (方案B)  
3. **优化初始化顺序** (方案C)

### 7.2 实施优先级
1. **高优先级**: 方案B - 修复PID_Init函数
2. **中优先级**: 方案C - 调整初始化顺序
3. **低优先级**: 方案A - 显式结构体初始化

### 7.3 验证方法
1. **调试验证**: 在PID_Init后检查Out值是否为0
2. **示波器验证**: 观察上电时PWM输出波形
3. **多次测试**: 连续上电100次验证一致性

## 8. 风险评估

### 8.1 实施风险
| 风险 | 影响 | 概率 | 缓解措施 |
|------|------|------|----------|
| 修改引入新问题 | 中 | 低 | 充分测试，保留回退方案 |
| 初始化时间增加 | 低 | 低 | 优化初始化代码 |
| 内存使用变化 | 低 | 极低 | 监控内存使用情况 |

### 8.2 不修复的风险
- 用户体验差，系统看起来不稳定
- 可能影响电机寿命
- 测试结果不一致

## 9. 版本历史

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| v1.0 | 2025-07-31 | 初版创建，完成问题分析和解决方案建议 | Emma |

---

**文档状态**: ✅ 需求分析完成  
**关键发现**: PID结构体初始化不完整，需要显式清零所有成员  
**推荐方案**: 组合使用显式初始化、增强PID_Init函数和优化初始化顺序  
**下一步**: 进入硬件初始化架构检查阶段
