******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 14:25:29 2025

OUTPUT FILE NAME:   <TI_CAR1.8.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0000205d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00002720  0001d8e0  R  X
  SRAM                  20200000   00008000  000008f2  0000770e  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00002720   00002720    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00002570   00002570    r-x .text
  00002630    00002630    00000098   00000098    r-- .rodata
  000026c8    000026c8    00000058   00000058    r-- .cinit
20200000    20200000    000006f2   00000000    rw-
  20200000    20200000    00000400   00000000    rw- .sysmem
  20200400    20200400    0000023c   00000000    rw- .bss
  2020063c    2020063c    000000b6   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00002570     
                  000000c0    000001d0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000290    000001b0     Task.o (.text.Task_Start)
                  00000440    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000005d2    00000002     Task.o (.text.Task_IdleFunction)
                  000005d4    00000190     Tracker.o (.text.Tracker_Read)
                  00000764    00000144     PID.o (.text.PID_SProsc)
                  000008a8    00000134     libc.a : qsort.c.obj (.text.qsort)
                  000009dc    00000110     Task_App.o (.text.Task_Motor_PID)
                  00000aec    0000010c     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00000bf8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00000d04    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00000e08    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00000eec    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00000fc8    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000010a0    000000d4     Motor.o (.text.Motor_SetDuty)
                  00001174    000000d4     Task_App.o (.text.Task_Key)
                  00001248    000000b4     Task.o (.text.Task_Add)
                  000012fc    000000b0     Interrupt.o (.text.GROUP1_IRQHandler)
                  000013ac    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorAFront_init)
                  00001438    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBFront_init)
                  000014c4    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00001550    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000015d4    0000007c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001650    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000016cc    00000074     Motor.o (.text.Motor_Start)
                  00001740    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000017b4    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00001818    00000064     Key_Led.o (.text.Key_Read)
                  0000187c    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000018de    00000002     --HOLE-- [fill = 0]
                  000018e0    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00001940    0000005c     Task_App.o (.text.Task_Init)
                  0000199c    0000005c     Task_App.o (.text.Task_Tracker)
                  000019f8    00000054     Motor.o (.text.CalculateDutyValue)
                  00001a4c    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00001aa0    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00001af0    00000048     Motor.o (.text.Motor_GetSpeed)
                  00001b38    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00001b7c    00000044     Motor.o (.text.SetPWMValue)
                  00001bc0    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00001c02    00000002     --HOLE-- [fill = 0]
                  00001c04    00000040     Interrupt.o (.text.Interrupt_Init)
                  00001c44    00000040     libclang_rt.builtins.a : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00001c84    0000003e     Task.o (.text.Task_CMP)
                  00001cc2    00000002     --HOLE-- [fill = 0]
                  00001cc4    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00001d00    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00001d3c    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00001d78    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00001db4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001df0    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00001e2a    00000002     --HOLE-- [fill = 0]
                  00001e2c    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00001e66    00000002     --HOLE-- [fill = 0]
                  00001e68    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00001ea0    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00001ed4    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00001f08    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00001f38    0000002c              : _IQNmpy.o (.text._IQ24mpy)
                  00001f64    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00001f90    0000002a     PID.o (.text.PID_Init)
                  00001fba    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00001fe2    00000002     --HOLE-- [fill = 0]
                  00001fe4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  0000200c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00002034    00000028     SysTick.o (.text.SysTick_Increasment)
                  0000205c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00002084    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000020aa    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000020d0    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  000020f4    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00002118    00000022     PID.o (.text.PID_SetParams)
                  0000213a    00000002     --HOLE-- [fill = 0]
                  0000213c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  0000215c    00000020     main.o (.text.main)
                  0000217c    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00002198    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000021b4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000021d0    0000001c     Interrupt.o (.text.DL_GPIO_enableInterrupt)
                  000021ec    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00002208    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00002224    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00002240    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  0000225c    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00002278    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00002294    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000022b0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000022cc    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000022e8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00002300    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00002318    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00002330    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00002348    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00002360    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00002378    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00002390    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000023a8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000023c0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000023d8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000023f0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00002408    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00002420    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00002438    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00002450    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00002468    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00002480    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00002498    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000024b0    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000024c8    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000024e0    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000024f6    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  0000250c    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  00002522    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00002538    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  0000254c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00002560    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00002574    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00002588    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  0000259c    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000025ae    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000025c0    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000025d0    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000025e0    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000025f0    0000000c     SysTick.o (.text.Sys_GetTick)
                  000025fc    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00002606    00000008     Interrupt.o (.text.SysTick_Handler)
                  0000260e    00000002     --HOLE-- [fill = 0]
                  00002610    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00002618    00000006     libc.a : exit.c.obj (.text:abort)
                  0000261e    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00002622    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00002626    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000262a    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  0000262e    00000002     --HOLE-- [fill = 0]

.cinit     0    000026c8    00000058     
                  000026c8    00000033     (.cinit..data.load) [load image, compression = lzss]
                  000026fb    00000001     --HOLE-- [fill = 0]
                  000026fc    0000000c     (__TI_handler_table)
                  00002708    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00002710    00000010     (__TI_cinit_table)

.rodata    0    00002630    00000098     
                  00002630    00000041     iqmath.a : _IQNtables.o (.rodata._IQ6div_lookup)
                  00002671    00000003     ti_msp_dl_config.o (.rodata.gMotorAFrontClockConfig)
                  00002674    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000269c    00000008     ti_msp_dl_config.o (.rodata.gMotorAFrontConfig)
                  000026a4    00000008     ti_msp_dl_config.o (.rodata.gMotorBFrontConfig)
                  000026ac    00000008     Task_App.o (.rodata.str1.10635198597896025474.1)
                  000026b4    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  000026ba    00000004     Task_App.o (.rodata.str1.12629676409056169537.1)
                  000026be    00000003     ti_msp_dl_config.o (.rodata.gMotorBFrontClockConfig)
                  000026c1    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000026c3    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000400     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000003f0     --HOLE--

.bss       0    20200400    0000023c     UNINITIALIZED
                  20200400    000000f0     Task.o (.bss.Task_Schedule)
                  202004f0    000000a0     (.common:gMotorAFrontBackup)
                  20200590    000000a0     (.common:gMotorBFrontBackup)
                  20200630    00000007     (.common:tick)
                  20200637    00000001     Task_App.o (.bss.Task_Key.Key_Old)
                  20200638    00000004     (.common:ExISR_Flag)

.data      0    2020063c    000000b6     UNINITIALIZED
                  2020063c    00000048     Motor.o (.data.Motor_Font_Left)
                  20200684    00000048     Motor.o (.data.Motor_Font_Right)
                  202006cc    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202006d4    00000008     Task_App.o (.data.Motor)
                  202006dc    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202006e0    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202006e4    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202006e8    00000004     SysTick.o (.data.delayTick)
                  202006ec    00000004     SysTick.o (.data.uwTick)
                  202006f0    00000001     Task_App.o (.data.Motor_Flag)
                  202006f1    00000001     Task.o (.data.Task_Num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             2418   64        320    
       startup_mspm0g350x_ticlang.o   8      192       0      
       main.o                         32     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         2458   256       320    
                                                              
    .\APP\Src\
       Task_App.o                     712    18        30     
       Interrupt.o                    422    0         4      
    +--+------------------------------+------+---------+---------+
       Total:                         1134   18        34     
                                                              
    .\BSP\Src\
       Task.o                         676    0         241    
       Motor.o                        576    0         144    
       Tracker.o                      422    0         7      
       PID.o                          400    0         0      
       Key_Led.o                      122    0         0      
       SysTick.o                      52     0         8      
    +--+------------------------------+------+---------+---------+
       Total:                         2248   0         400    
                                                              
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388    0         0      
       dl_timer.o                     356    0         0      
       dl_i2c.o                       38     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         792    0         0      
                                                              
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/rts/mspm0g1x0x_g3x0x/iqmath.a
       _IQNdiv.o                      268    0         0      
       _IQNtables.o                   0      65        0      
       _IQNtoF.o                      48     0         0      
       _IQNmpy.o                      44     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         360    65        0      
                                                              
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       qsort.c.obj                    308    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         600    0         0      
                                                              
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   402    0         0      
       divdf3.S.obj                   268    0         0      
       muldf3.S.obj                   228    0         0      
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       comparesf2.S.obj               118    0         0      
       truncdfsf2.S.obj               116    0         0      
       aeabi_fcmp.S.obj               98     0         0      
       fixunsdfsi.S.obj               66     0         0      
       extendsfdf2.S.obj              64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       floatunsidf.S.obj              36     0         0      
       muldi3.S.obj                   36     0         0      
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1970   0         0      
                                                              
       Heap:                          0      0         1024   
       Stack:                         0      0         512    
       Linker Generated:              0      87        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   9566   426       2290   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00002710 records: 2, size/record: 8, table size: 16
	.data: load addr=000026c8, load size=00000033 bytes, run addr=2020063c, run size=000000b6 bytes, compression=lzss
	.bss: load addr=00002708, load size=00000008 bytes, run addr=20200400, run size=0000023c bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000026fc records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000261f  ADC0_IRQHandler                      
0000261f  ADC1_IRQHandler                      
0000261f  AES_IRQHandler                       
00002622  C$$EXIT                              
0000261f  CANFD0_IRQHandler                    
0000261f  DAC0_IRQHandler                      
000025fd  DL_Common_delayCycles                
000020ab  DL_I2C_setClockConfig                
00000eed  DL_SYSCTL_configSYSPLL               
000017b5  DL_SYSCTL_setHFCLKSourceHFXTParams   
00001b39  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00000d05  DL_Timer_initFourCCPWMMode           
000022b1  DL_Timer_setCaptCompUpdateMethod     
000024b1  DL_Timer_setCaptureCompareOutCtl     
000025d1  DL_Timer_setCaptureCompareValue      
000022cd  DL_Timer_setClockConfig              
0000261f  DMA_IRQHandler                       
202006dc  Data_MotorEncoder                    
202006e0  Data_Motor_TarSpeed                  
202006cc  Data_Tracker_Input                   
202006e4  Data_Tracker_Offset                  
0000261f  Default_Handler                      
20200638  ExISR_Flag                           
0000261f  GROUP0_IRQHandler                    
000012fd  GROUP1_IRQHandler                    
00002623  HOSTexit                             
0000261f  HardFault_Handler                    
0000261f  I2C0_IRQHandler                      
0000261f  I2C1_IRQHandler                      
00001c05  Interrupt_Init                       
00001819  Key_Read                             
202006d4  Motor                                
202006f0  Motor_Flag                           
2020063c  Motor_Font_Left                      
20200684  Motor_Font_Right                     
00001af1  Motor_GetSpeed                       
000010a1  Motor_SetDuty                        
000016cd  Motor_Start                          
0000261f  NMI_Handler                          
00001f91  PID_Init                             
00000765  PID_SProsc                           
00002119  PID_SetParams                        
0000261f  PendSV_Handler                       
0000261f  RTC_IRQHandler                       
00002627  Reset_Handler                        
0000261f  SPI0_IRQHandler                      
0000261f  SPI1_IRQHandler                      
0000261f  SVC_Handler                          
000000c1  SYSCFG_DL_GPIO_init                  
000018e1  SYSCFG_DL_I2C_OLED_init              
000013ad  SYSCFG_DL_MotorAFront_init           
00001439  SYSCFG_DL_MotorBFront_init           
00001a4d  SYSCFG_DL_SYSCTL_init                
000025e1  SYSCFG_DL_SYSTICK_init               
00001ed5  SYSCFG_DL_init                       
000015d5  SYSCFG_DL_initPower                  
00002607  SysTick_Handler                      
00002035  SysTick_Increasment                  
000025f1  Sys_GetTick                          
0000261f  TIMA0_IRQHandler                     
0000261f  TIMA1_IRQHandler                     
0000261f  TIMG0_IRQHandler                     
0000261f  TIMG12_IRQHandler                    
0000261f  TIMG6_IRQHandler                     
0000261f  TIMG7_IRQHandler                     
0000261f  TIMG8_IRQHandler                     
0000259d  TI_memcpy_small                      
00001249  Task_Add                             
000005d3  Task_IdleFunction                    
00001941  Task_Init                            
00001175  Task_Key                             
000009dd  Task_Motor_PID                       
00000291  Task_Start                           
0000199d  Task_Tracker                         
000005d5  Tracker_Read                         
0000261f  UART0_IRQHandler                     
0000261f  UART1_IRQHandler                     
0000261f  UART2_IRQHandler                     
0000261f  UART3_IRQHandler                     
00000aed  _IQ24div                             
00001f39  _IQ24mpy                             
00001f09  _IQ24toF                             
00002630  _IQ6div_lookup                       
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00002710  __TI_CINIT_Base                      
00002720  __TI_CINIT_Limit                     
00002720  __TI_CINIT_Warm                      
000026fc  __TI_Handler_Table_Base              
00002708  __TI_Handler_Table_Limit             
00001db5  __TI_auto_init_nobinit_nopinit       
00001651  __TI_decompress_lzss                 
000025af  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00002523  __TI_zero_init_nomemset              
0000044b  __adddf3                             
00000fd3  __addsf3                             
00001741  __aeabi_d2f                          
00001bc1  __aeabi_d2uiz                        
0000044b  __aeabi_dadd                         
00000bf9  __aeabi_ddiv                         
00000e09  __aeabi_dmul                         
00000441  __aeabi_dsub                         
00001c45  __aeabi_f2d                          
00001e69  __aeabi_f2iz                         
00000fd3  __aeabi_fadd                         
0000187d  __aeabi_fcmpeq                       
000018b9  __aeabi_fcmpge                       
000018cd  __aeabi_fcmpgt                       
000018a5  __aeabi_fcmple                       
00001891  __aeabi_fcmplt                       
000014c5  __aeabi_fmul                         
00000fc9  __aeabi_fsub                         
00001d3d  __aeabi_i2f                          
000020f5  __aeabi_lmul                         
00002611  __aeabi_memcpy                       
00002611  __aeabi_memcpy4                      
00002611  __aeabi_memcpy8                      
000020d1  __aeabi_ui2d                         
ffffffff  __binit__                            
00001df1  __cmpsf2                             
00000bf9  __divdf3                             
00001df1  __eqsf2                              
00001c45  __extendsfdf2                        
00001e69  __fixsfsi                            
00001bc1  __fixunsdfsi                         
00001d3d  __floatsisf                          
000020d1  __floatunsidf                        
00001d79  __gesf2                              
00001d79  __gtsf2                              
00001df1  __lesf2                              
00001df1  __ltsf2                              
UNDEFED   __mpu_init                           
00000e09  __muldf3                             
000020f5  __muldi3                             
00001e2d  __muldsi3                            
000014c5  __mulsf3                             
00001df1  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00000441  __subdf3                             
00000fc9  __subsf3                             
00001741  __truncdfsf2                         
0000205d  _c_int00_noargs                      
20200000  _sys_memory                          
UNDEFED   _system_post_cinit                   
0000262b  _system_pre_init                     
00002619  abort                                
ffffffff  binit                                
202006e8  delayTick                            
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
00000000  interruptVectors                     
0000215d  main                                 
000008a9  qsort                                
20200630  tick                                 
202006ec  uwTick                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  SYSCFG_DL_GPIO_init                  
00000200  __STACK_SIZE                         
00000291  Task_Start                           
00000400  __SYSMEM_SIZE                        
00000441  __aeabi_dsub                         
00000441  __subdf3                             
0000044b  __adddf3                             
0000044b  __aeabi_dadd                         
000005d3  Task_IdleFunction                    
000005d5  Tracker_Read                         
00000765  PID_SProsc                           
000008a9  qsort                                
000009dd  Task_Motor_PID                       
00000aed  _IQ24div                             
00000bf9  __aeabi_ddiv                         
00000bf9  __divdf3                             
00000d05  DL_Timer_initFourCCPWMMode           
00000e09  __aeabi_dmul                         
00000e09  __muldf3                             
00000eed  DL_SYSCTL_configSYSPLL               
00000fc9  __aeabi_fsub                         
00000fc9  __subsf3                             
00000fd3  __addsf3                             
00000fd3  __aeabi_fadd                         
000010a1  Motor_SetDuty                        
00001175  Task_Key                             
00001249  Task_Add                             
000012fd  GROUP1_IRQHandler                    
000013ad  SYSCFG_DL_MotorAFront_init           
00001439  SYSCFG_DL_MotorBFront_init           
000014c5  __aeabi_fmul                         
000014c5  __mulsf3                             
000015d5  SYSCFG_DL_initPower                  
00001651  __TI_decompress_lzss                 
000016cd  Motor_Start                          
00001741  __aeabi_d2f                          
00001741  __truncdfsf2                         
000017b5  DL_SYSCTL_setHFCLKSourceHFXTParams   
00001819  Key_Read                             
0000187d  __aeabi_fcmpeq                       
00001891  __aeabi_fcmplt                       
000018a5  __aeabi_fcmple                       
000018b9  __aeabi_fcmpge                       
000018cd  __aeabi_fcmpgt                       
000018e1  SYSCFG_DL_I2C_OLED_init              
00001941  Task_Init                            
0000199d  Task_Tracker                         
00001a4d  SYSCFG_DL_SYSCTL_init                
00001af1  Motor_GetSpeed                       
00001b39  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001bc1  __aeabi_d2uiz                        
00001bc1  __fixunsdfsi                         
00001c05  Interrupt_Init                       
00001c45  __aeabi_f2d                          
00001c45  __extendsfdf2                        
00001d3d  __aeabi_i2f                          
00001d3d  __floatsisf                          
00001d79  __gesf2                              
00001d79  __gtsf2                              
00001db5  __TI_auto_init_nobinit_nopinit       
00001df1  __cmpsf2                             
00001df1  __eqsf2                              
00001df1  __lesf2                              
00001df1  __ltsf2                              
00001df1  __nesf2                              
00001e2d  __muldsi3                            
00001e69  __aeabi_f2iz                         
00001e69  __fixsfsi                            
00001ed5  SYSCFG_DL_init                       
00001f09  _IQ24toF                             
00001f39  _IQ24mpy                             
00001f91  PID_Init                             
00002035  SysTick_Increasment                  
0000205d  _c_int00_noargs                      
000020ab  DL_I2C_setClockConfig                
000020d1  __aeabi_ui2d                         
000020d1  __floatunsidf                        
000020f5  __aeabi_lmul                         
000020f5  __muldi3                             
00002119  PID_SetParams                        
0000215d  main                                 
000022b1  DL_Timer_setCaptCompUpdateMethod     
000022cd  DL_Timer_setClockConfig              
000024b1  DL_Timer_setCaptureCompareOutCtl     
00002523  __TI_zero_init_nomemset              
0000259d  TI_memcpy_small                      
000025af  __TI_decompress_none                 
000025d1  DL_Timer_setCaptureCompareValue      
000025e1  SYSCFG_DL_SYSTICK_init               
000025f1  Sys_GetTick                          
000025fd  DL_Common_delayCycles                
00002607  SysTick_Handler                      
00002611  __aeabi_memcpy                       
00002611  __aeabi_memcpy4                      
00002611  __aeabi_memcpy8                      
00002619  abort                                
0000261f  ADC0_IRQHandler                      
0000261f  ADC1_IRQHandler                      
0000261f  AES_IRQHandler                       
0000261f  CANFD0_IRQHandler                    
0000261f  DAC0_IRQHandler                      
0000261f  DMA_IRQHandler                       
0000261f  Default_Handler                      
0000261f  GROUP0_IRQHandler                    
0000261f  HardFault_Handler                    
0000261f  I2C0_IRQHandler                      
0000261f  I2C1_IRQHandler                      
0000261f  NMI_Handler                          
0000261f  PendSV_Handler                       
0000261f  RTC_IRQHandler                       
0000261f  SPI0_IRQHandler                      
0000261f  SPI1_IRQHandler                      
0000261f  SVC_Handler                          
0000261f  TIMA0_IRQHandler                     
0000261f  TIMA1_IRQHandler                     
0000261f  TIMG0_IRQHandler                     
0000261f  TIMG12_IRQHandler                    
0000261f  TIMG6_IRQHandler                     
0000261f  TIMG7_IRQHandler                     
0000261f  TIMG8_IRQHandler                     
0000261f  UART0_IRQHandler                     
0000261f  UART1_IRQHandler                     
0000261f  UART2_IRQHandler                     
0000261f  UART3_IRQHandler                     
00002622  C$$EXIT                              
00002623  HOSTexit                             
00002627  Reset_Handler                        
0000262b  _system_pre_init                     
00002630  _IQ6div_lookup                       
000026fc  __TI_Handler_Table_Base              
00002708  __TI_Handler_Table_Limit             
00002710  __TI_CINIT_Base                      
00002720  __TI_CINIT_Limit                     
00002720  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  _sys_memory                          
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
20200630  tick                                 
20200638  ExISR_Flag                           
2020063c  Motor_Font_Left                      
20200684  Motor_Font_Right                     
202006cc  Data_Tracker_Input                   
202006d4  Motor                                
202006dc  Data_MotorEncoder                    
202006e0  Data_Motor_TarSpeed                  
202006e4  Data_Tracker_Offset                  
202006e8  delayTick                            
202006ec  uwTick                               
202006f0  Motor_Flag                           
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[173 symbols]
