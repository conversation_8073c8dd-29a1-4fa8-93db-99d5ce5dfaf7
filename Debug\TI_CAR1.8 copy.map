******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 03:37:52 2025

OUTPUT FILE NAME:   <TI_CAR1.8 copy.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003f85


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000052a0  0001ad60  R  X
  SRAM                  20200000   00008000  000009b9  00007647  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004828   00004828    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004768   00004768    r-x .text
00004830    00004830    00000a78   00000a78    r--
  00004830    00004830    00000a20   00000a20    r-- .rodata
  00005250    00005250    00000058   00000058    r-- .cinit
20200000    20200000    000007bb   00000000    rw-
  20200000    20200000    00000400   00000000    rw- .sysmem
  20200400    20200400    000002fa   00000000    rw- .bss
  202006fc    202006fc    000000bf   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004768     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000300     Tracker.o (.text.Tracker_Read)
                  00000d90    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00000fb0    000001dc            : _printfi.c.obj (.text._pconv_g)
                  0000118c    000001d0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000135c    000001b0     Task.o (.text.Task_Start)
                  0000150c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000169e    00000002     Task.o (.text.Task_IdleFunction)
                  000016a0    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000017dc    00000134            : qsort.c.obj (.text.qsort)
                  00001910    00000130     OLED.o (.text.OLED_ShowChar)
                  00001a40    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001b60    00000118     Task_App.o (.text.Task_Motor_PID)
                  00001c78    00000110     OLED.o (.text.OLED_Init)
                  00001d88    0000010c     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00001e94    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001fa0    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000020a4    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  0000218c    000000e4     Task_App.o (.text.Task_Key)
                  00002270    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002354    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002430    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002508    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000025e0    000000d4     Motor.o (.text.Motor_SetDuty)
                  000026b4    000000c4     PID.o (.text.PID_SProsc)
                  00002778    000000b4     Task.o (.text.Task_Add)
                  0000282c    000000b0     Interrupt.o (.text.GROUP1_IRQHandler)
                  000028dc    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  0000297e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002980    000000a0     Task_App.o (.text.Task_OLED)
                  00002a20    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00002ab8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorAFront_init)
                  00002b44    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBFront_init)
                  00002bd0    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002c5c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002ce8    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002d6c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002de8    00000074     Motor.o (.text.Motor_Start)
                  00002e5c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002ed0    0000006e     OLED.o (.text.OLED_ShowString)
                  00002f3e    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00002fa8    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00003010    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00003076    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00003078    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  000030dc    00000064     Key_Led.o (.text.Key_Read)
                  00003140    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000031a2    00000002     --HOLE-- [fill = 0]
                  000031a4    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00003206    00000002     --HOLE-- [fill = 0]
                  00003208    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00003268    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000032c8    00000060     Task_App.o (.text.Task_Init)
                  00003328    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003386    00000002     --HOLE-- [fill = 0]
                  00003388    0000005c     Task_App.o (.text.Task_Tracker)
                  000033e4    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003440    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  0000349c    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000034f4    00000058            : _printfi.c.obj (.text._pconv_f)
                  0000354c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000035a2    00000002     --HOLE-- [fill = 0]
                  000035a4    00000054     Motor.o (.text.CalculateDutyValue)
                  000035f8    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000364c    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  0000369e    00000002     --HOLE-- [fill = 0]
                  000036a0    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  000036f0    00000050     Interrupt.o (.text.Interrupt_Init)
                  00003740    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00003790    0000004c     OLED.o (.text.OLED_Printf)
                  000037dc    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00003826    00000048     Motor.o (.text.Motor_GetSpeed)
                  0000386e    00000002     --HOLE-- [fill = 0]
                  00003870    00000048     OLED.o (.text.mspm0_i2c_disable)
                  000038b8    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000038fc    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003940    00000044     Motor.o (.text.SetPWMValue)
                  00003984    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000039c6    00000002     --HOLE-- [fill = 0]
                  000039c8    00000040                            : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003a08    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00003a48    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00003a88    0000003e     Task.o (.text.Task_CMP)
                  00003ac6    00000002     --HOLE-- [fill = 0]
                  00003ac8    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003b04    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003b40    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00003b7c    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00003bb8    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00003bf4    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003c30    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003c6c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003ca6    00000002     --HOLE-- [fill = 0]
                  00003ca8    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003ce2    00000002     --HOLE-- [fill = 0]
                  00003ce4    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003d18    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003d4c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00003d80    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00003db0    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00003de0    0000002c     iqmath.a : _IQNmpy.o (.text._IQ24mpy)
                  00003e0c    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00003e38    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003e64    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00003e90    0000002a     PID.o (.text.PID_Init)
                  00003eba    00000028     OLED.o (.text.DL_Common_updateReg)
                  00003ee2    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003f0a    00000002     --HOLE-- [fill = 0]
                  00003f0c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003f34    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003f5c    00000028     SysTick.o (.text.SysTick_Increasment)
                  00003f84    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003fac    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003fd2    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003ff8    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  0000401c    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00004040    00000022     PID.o (.text.PID_SetParams)
                  00004062    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004084    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000040a4    00000020     SysTick.o (.text.Delay)
                  000040c4    00000020     main.o (.text.main)
                  000040e4    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00004102    00000002     --HOLE-- [fill = 0]
                  00004104    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00004120    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  0000413c    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00004158    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00004174    0000001c     Interrupt.o (.text.DL_GPIO_enableInterrupt)
                  00004190    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000041ac    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000041c8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  000041e4    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00004200    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  0000421c    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00004238    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00004254    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00004270    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  0000428c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000042a8    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000042c4    0000001c     Task_App.o (.text.TIMA0_IRQHandler)
                  000042e0    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  000042f8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00004310    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00004328    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00004340    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00004358    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00004370    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00004388    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000043a0    00000018     OLED.o (.text.DL_GPIO_setPins)
                  000043b8    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  000043d0    00000018     Tracker.o (.text.DL_GPIO_setPins)
                  000043e8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00004400    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00004418    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00004430    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00004448    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00004460    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00004478    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00004490    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000044a8    00000018     OLED.o (.text.DL_I2C_reset)
                  000044c0    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000044d8    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000044f0    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00004508    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00004520    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00004538    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00004550    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00004568    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004580    00000018     Interrupt.o (.text.DL_Timer_startCounter)
                  00004598    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000045b0    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  000045c8    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000045de    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  000045f4    00000016     OLED.o (.text.DL_GPIO_readPins)
                  0000460a    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  00004620    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00004636    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  0000464a    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  0000465e    00000014     Tracker.o (.text.DL_GPIO_clearPins)
                  00004672    00000002     --HOLE-- [fill = 0]
                  00004674    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00004688    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  0000469c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000046b0    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000046c4    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000046d8    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000046ec    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00004700    00000012     Task_App.o (.text.DL_Timer_getPendingInterrupt)
                  00004712    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00004724    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00004736    00000002     --HOLE-- [fill = 0]
                  00004738    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00004748    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00004758    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00004768    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004778    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00004786    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00004794    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  000047a2    00000002     --HOLE-- [fill = 0]
                  000047a4    0000000c     SysTick.o (.text.Sys_GetTick)
                  000047b0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000047ba    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000047c4    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000047d4    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000047de    0000000a            : vsprintf.c.obj (.text._outc)
                  000047e8    00000008     Interrupt.o (.text.SysTick_Handler)
                  000047f0    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000047f8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00004800    00000006     libc.a : exit.c.obj (.text:abort)
                  00004806    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000480a    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0000480e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00004812    00000002     --HOLE-- [fill = 0]
                  00004814    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00004824    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    00005250    00000058     
                  00005250    00000034     (.cinit..data.load) [load image, compression = lzss]
                  00005284    0000000c     (__TI_handler_table)
                  00005290    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00005298    00000010     (__TI_cinit_table)

.rodata    0    00004830    00000a20     
                  00004830    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00004e20    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00005048    00000008     ti_msp_dl_config.o (.rodata.gMotorAFrontConfig)
                  00005050    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00005151    00000041     iqmath.a : _IQNtables.o (.rodata._IQ6div_lookup)
                  00005192    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00005194    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000051bc    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  000051d0    00000014     Tracker.o (.rodata.str1.18388326728890721169.1)
                  000051e4    00000012     Task_App.o (.rodata.str1.16020955549137178199.1)
                  000051f6    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00005207    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00005218    0000000d     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00005225    00000003     ti_msp_dl_config.o (.rodata.gMotorAFrontClockConfig)
                  00005228    00000008     ti_msp_dl_config.o (.rodata.gMotorBFrontConfig)
                  00005230    00000008     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00005238    00000006     Task_App.o (.rodata.str1.11683036942922059812.1)
                  0000523e    00000005     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00005243    00000004     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00005247    00000003     ti_msp_dl_config.o (.rodata.gMotorBFrontClockConfig)
                  0000524a    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  0000524d    00000003     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000400     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000003f0     --HOLE--

.bss       0    20200400    000002fa     UNINITIALIZED
                  20200400    000000f0     Task.o (.bss.Task_Schedule)
                  202004f0    000000bc     (.common:gTIMER_0Backup)
                  202005ac    000000a0     (.common:gMotorAFrontBackup)
                  2020064c    000000a0     (.common:gMotorBFrontBackup)
                  202006ec    00000007     (.common:tick)
                  202006f3    00000001     Task_App.o (.bss.Task_Key.Key_Old)
                  202006f4    00000004     (.common:ExISR_Flag)
                  202006f8    00000001     (.common:Cycle)
                  202006f9    00000001     (.common:set_Cycle)

.data      0    202006fc    000000bf     UNINITIALIZED
                  202006fc    00000048     Motor.o (.data.Motor_Font_Left)
                  20200744    00000048     Motor.o (.data.Motor_Font_Right)
                  2020078c    00000008     Task_App.o (.data.Motor)
                  20200794    00000007     Task_App.o (.data.Data_Tracker_Input)
                  2020079b    00000001     Tracker.o (.data.Enable_Flag)
                  2020079c    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202007a0    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202007a4    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202007a8    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202007ac    00000004     SysTick.o (.data.delayTick)
                  202007b0    00000004     Tracker.o (.data.turn_start_time)
                  202007b4    00000004     SysTick.o (.data.uwTick)
                  202007b8    00000001     Task_App.o (.data.Motor_Flag)
                  202007b9    00000001     Task.o (.data.Task_Num)
                  202007ba    00000001     Tracker.o (.data.is_turning)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2530    87        508    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         1      
    +--+------------------------------+-------+---------+---------+
       Total:                         2570    279       509    
                                                               
    .\APP\Src\
       Task_App.o                     946     54        30     
       Interrupt.o                    462     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         1408    54        34     
                                                               
    .\BSP\Src\
       OLED_Font.o                    0       2072      0      
       OLED.o                         1858    0         0      
       Task.o                         676     0         241    
       Tracker.o                      834     20        13     
       Motor.o                        576     0         144    
       PID.o                          272     0         0      
       Key_Led.o                      122     0         0      
       SysTick.o                      84      0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         4422    2092      406    
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_i2c.o                       132     0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1118    0         0      
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/rts/mspm0g1x0x_g3x0x/iqmath.a
       _IQNdiv.o                      268     0         0      
       _IQNtables.o                   0       65        0      
       _IQNtoF.o                      48      0         0      
       _IQNmpy.o                      44      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         360     65        0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5736    291       4      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       comparesf2.S.obj               118     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2630    0         0      
                                                               
       Heap:                          0       0         1024   
       Stack:                         0       0         512    
       Linker Generated:              0       88        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   18248   2869      2489   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00005298 records: 2, size/record: 8, table size: 16
	.data: load addr=00005250, load size=00000034 bytes, run addr=202006fc, run size=000000bf bytes, compression=lzss
	.bss: load addr=00005290, load size=00000008 bytes, run addr=20200400, run size=000002fa bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00005284 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   0000150d     000047c4     000047c2   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003f85     00004814     0000480e   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00004807  ADC0_IRQHandler                      
00004807  ADC1_IRQHandler                      
00004807  AES_IRQHandler                       
0000480a  C$$EXIT                              
00004807  CANFD0_IRQHandler                    
202006f8  Cycle                                
00004807  DAC0_IRQHandler                      
000047b1  DL_Common_delayCycles                
00003329  DL_I2C_fillControllerTXFIFO          
00003fd3  DL_I2C_setClockConfig                
00002355  DL_SYSCTL_configSYSPLL               
00003079  DL_SYSCTL_setHFCLKSourceHFXTParams   
000038b9  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001fa1  DL_Timer_initFourCCPWMMode           
000020a5  DL_Timer_initTimerMode               
0000428d  DL_Timer_setCaptCompUpdateMethod     
00004569  DL_Timer_setCaptureCompareOutCtl     
00004749  DL_Timer_setCaptureCompareValue      
000042a9  DL_Timer_setClockConfig              
00004807  DMA_IRQHandler                       
2020079c  Data_MotorEncoder                    
202007a0  Data_Motor_TarSpeed                  
20200794  Data_Tracker_Input                   
202007a4  Data_Tracker_Offset                  
00004807  Default_Handler                      
000040a5  Delay                                
202006f4  ExISR_Flag                           
00004807  GROUP0_IRQHandler                    
0000282d  GROUP1_IRQHandler                    
0000480b  HOSTexit                             
00004807  HardFault_Handler                    
00004807  I2C0_IRQHandler                      
00004807  I2C1_IRQHandler                      
00002f3f  I2C_OLED_Clear                       
00003b7d  I2C_OLED_Set_Pos                     
00002a21  I2C_OLED_WR_Byte                     
00003209  I2C_OLED_i2c_sda_unlock              
000036f1  Interrupt_Init                       
000030dd  Key_Read                             
2020078c  Motor                                
202007b8  Motor_Flag                           
202006fc  Motor_Font_Left                      
20200744  Motor_Font_Right                     
00003827  Motor_GetSpeed                       
000025e1  Motor_SetDuty                        
00002de9  Motor_Start                          
00004807  NMI_Handler                          
00001c79  OLED_Init                            
00003791  OLED_Printf                          
00001911  OLED_ShowChar                        
00002ed1  OLED_ShowString                      
00003e91  PID_Init                             
000026b5  PID_SProsc                           
00004041  PID_SetParams                        
00004807  PendSV_Handler                       
00004807  RTC_IRQHandler                       
0000480f  Reset_Handler                        
00004807  SPI0_IRQHandler                      
00004807  SPI1_IRQHandler                      
00004807  SVC_Handler                          
0000118d  SYSCFG_DL_GPIO_init                  
00003269  SYSCFG_DL_I2C_OLED_init              
00002ab9  SYSCFG_DL_MotorAFront_init           
00002b45  SYSCFG_DL_MotorBFront_init           
000035f9  SYSCFG_DL_SYSCTL_init                
00004759  SYSCFG_DL_SYSTICK_init               
00003d4d  SYSCFG_DL_TIMER_0_init               
000038fd  SYSCFG_DL_init                       
00002bd1  SYSCFG_DL_initPower                  
000047e9  SysTick_Handler                      
00003f5d  SysTick_Increasment                  
000047a5  Sys_GetTick                          
000042c5  TIMA0_IRQHandler                     
00004807  TIMA1_IRQHandler                     
00004807  TIMG0_IRQHandler                     
00004807  TIMG12_IRQHandler                    
00004807  TIMG6_IRQHandler                     
00004807  TIMG7_IRQHandler                     
00004807  TIMG8_IRQHandler                     
00004713  TI_memcpy_small                      
00004795  TI_memset_small                      
00002779  Task_Add                             
0000169f  Task_IdleFunction                    
000032c9  Task_Init                            
0000218d  Task_Key                             
00001b61  Task_Motor_PID                       
00002981  Task_OLED                            
0000135d  Task_Start                           
00003389  Task_Tracker                         
00000a91  Tracker_Read                         
00004807  UART0_IRQHandler                     
00004807  UART1_IRQHandler                     
00004807  UART2_IRQHandler                     
00004807  UART3_IRQHandler                     
00001d89  _IQ24div                             
00003de1  _IQ24mpy                             
00003d81  _IQ24toF                             
00005151  _IQ6div_lookup                       
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00005298  __TI_CINIT_Base                      
000052a8  __TI_CINIT_Limit                     
000052a8  __TI_CINIT_Warm                      
00005284  __TI_Handler_Table_Base              
00005290  __TI_Handler_Table_Limit             
00003c31  __TI_auto_init_nobinit_nopinit       
00002d6d  __TI_decompress_lzss                 
00004725  __TI_decompress_none                 
0000349d  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00004621  __TI_zero_init_nomemset              
00001517  __adddf3                             
00002513  __addsf3                             
00005050  __aeabi_ctype_table_                 
00005050  __aeabi_ctype_table_C                
000037dd  __aeabi_d2iz                         
00003985  __aeabi_d2uiz                        
00001517  __aeabi_dadd                         
00003141  __aeabi_dcmpeq                       
0000317d  __aeabi_dcmpge                       
00003191  __aeabi_dcmpgt                       
00003169  __aeabi_dcmple                       
00003155  __aeabi_dcmplt                       
00001e95  __aeabi_ddiv                         
00002271  __aeabi_dmul                         
0000150d  __aeabi_dsub                         
202007a8  __aeabi_errno                        
000047f1  __aeabi_errno_addr                   
00003a09  __aeabi_f2d                          
00002513  __aeabi_fadd                         
000031a5  __aeabi_fcmpeq                       
000031e1  __aeabi_fcmpge                       
000031f5  __aeabi_fcmpgt                       
000031cd  __aeabi_fcmple                       
000031b9  __aeabi_fcmplt                       
00002c5d  __aeabi_fmul                         
00002509  __aeabi_fsub                         
00003e39  __aeabi_i2d                          
00003bb9  __aeabi_i2f                          
0000354d  __aeabi_idiv                         
0000297f  __aeabi_idiv0                        
0000354d  __aeabi_idivmod                      
00003077  __aeabi_ldiv0                        
000040e5  __aeabi_llsl                         
0000401d  __aeabi_lmul                         
000047f9  __aeabi_memcpy                       
000047f9  __aeabi_memcpy4                      
000047f9  __aeabi_memcpy8                      
00004779  __aeabi_memset                       
00004779  __aeabi_memset4                      
00004779  __aeabi_memset8                      
00003ff9  __aeabi_ui2d                         
000039c9  __aeabi_uidiv                        
000039c9  __aeabi_uidivmod                     
000046d9  __aeabi_uldivmod                     
000040e5  __ashldi3                            
ffffffff  __binit__                            
00002fa9  __cmpdf2                             
00003c6d  __cmpsf2                             
00001e95  __divdf3                             
00002fa9  __eqdf2                              
00003c6d  __eqsf2                              
00003a09  __extendsfdf2                        
000037dd  __fixdfsi                            
00003985  __fixunsdfsi                         
00003e39  __floatsidf                          
00003bb9  __floatsisf                          
00003ff9  __floatunsidf                        
00002e5d  __gedf2                              
00003bf5  __gesf2                              
00002e5d  __gtdf2                              
00003bf5  __gtsf2                              
00002fa9  __ledf2                              
00003c6d  __lesf2                              
00002fa9  __ltdf2                              
00003c6d  __ltsf2                              
UNDEFED   __mpu_init                           
00002271  __muldf3                             
0000401d  __muldi3                             
00003ca9  __muldsi3                            
00002c5d  __mulsf3                             
00002fa9  __nedf2                              
00003c6d  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
0000150d  __subdf3                             
00002509  __subsf3                             
000028dd  __udivmoddi4                         
00003f85  _c_int00_noargs                      
20200000  _sys_memory                          
UNDEFED   _system_post_cinit                   
00004825  _system_pre_init                     
00004801  abort                                
00004e20  asc2_0806                            
00004830  asc2_1608                            
00003a49  atoi                                 
ffffffff  binit                                
202007ac  delayTick                            
000033e5  frexp                                
000033e5  frexpl                               
202005ac  gMotorAFrontBackup                   
2020064c  gMotorBFrontBackup                   
202004f0  gTIMER_0Backup                       
00000000  interruptVectors                     
202007ba  is_turning                           
00002431  ldexp                                
00002431  ldexpl                               
000040c5  main                                 
00004063  memccpy                              
000017dd  qsort                                
00002431  scalbn                               
00002431  scalbnl                              
202006f9  set_Cycle                            
202006ec  tick                                 
202007b4  uwTick                               
00003e65  vsprintf                             
00004769  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000a91  Tracker_Read                         
0000118d  SYSCFG_DL_GPIO_init                  
0000135d  Task_Start                           
0000150d  __aeabi_dsub                         
0000150d  __subdf3                             
00001517  __adddf3                             
00001517  __aeabi_dadd                         
0000169f  Task_IdleFunction                    
000017dd  qsort                                
00001911  OLED_ShowChar                        
00001b61  Task_Motor_PID                       
00001c79  OLED_Init                            
00001d89  _IQ24div                             
00001e95  __aeabi_ddiv                         
00001e95  __divdf3                             
00001fa1  DL_Timer_initFourCCPWMMode           
000020a5  DL_Timer_initTimerMode               
0000218d  Task_Key                             
00002271  __aeabi_dmul                         
00002271  __muldf3                             
00002355  DL_SYSCTL_configSYSPLL               
00002431  ldexp                                
00002431  ldexpl                               
00002431  scalbn                               
00002431  scalbnl                              
00002509  __aeabi_fsub                         
00002509  __subsf3                             
00002513  __addsf3                             
00002513  __aeabi_fadd                         
000025e1  Motor_SetDuty                        
000026b5  PID_SProsc                           
00002779  Task_Add                             
0000282d  GROUP1_IRQHandler                    
000028dd  __udivmoddi4                         
0000297f  __aeabi_idiv0                        
00002981  Task_OLED                            
00002a21  I2C_OLED_WR_Byte                     
00002ab9  SYSCFG_DL_MotorAFront_init           
00002b45  SYSCFG_DL_MotorBFront_init           
00002bd1  SYSCFG_DL_initPower                  
00002c5d  __aeabi_fmul                         
00002c5d  __mulsf3                             
00002d6d  __TI_decompress_lzss                 
00002de9  Motor_Start                          
00002e5d  __gedf2                              
00002e5d  __gtdf2                              
00002ed1  OLED_ShowString                      
00002f3f  I2C_OLED_Clear                       
00002fa9  __cmpdf2                             
00002fa9  __eqdf2                              
00002fa9  __ledf2                              
00002fa9  __ltdf2                              
00002fa9  __nedf2                              
00003077  __aeabi_ldiv0                        
00003079  DL_SYSCTL_setHFCLKSourceHFXTParams   
000030dd  Key_Read                             
00003141  __aeabi_dcmpeq                       
00003155  __aeabi_dcmplt                       
00003169  __aeabi_dcmple                       
0000317d  __aeabi_dcmpge                       
00003191  __aeabi_dcmpgt                       
000031a5  __aeabi_fcmpeq                       
000031b9  __aeabi_fcmplt                       
000031cd  __aeabi_fcmple                       
000031e1  __aeabi_fcmpge                       
000031f5  __aeabi_fcmpgt                       
00003209  I2C_OLED_i2c_sda_unlock              
00003269  SYSCFG_DL_I2C_OLED_init              
000032c9  Task_Init                            
00003329  DL_I2C_fillControllerTXFIFO          
00003389  Task_Tracker                         
000033e5  frexp                                
000033e5  frexpl                               
0000349d  __TI_ltoa                            
0000354d  __aeabi_idiv                         
0000354d  __aeabi_idivmod                      
000035f9  SYSCFG_DL_SYSCTL_init                
000036f1  Interrupt_Init                       
00003791  OLED_Printf                          
000037dd  __aeabi_d2iz                         
000037dd  __fixdfsi                            
00003827  Motor_GetSpeed                       
000038b9  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000038fd  SYSCFG_DL_init                       
00003985  __aeabi_d2uiz                        
00003985  __fixunsdfsi                         
000039c9  __aeabi_uidiv                        
000039c9  __aeabi_uidivmod                     
00003a09  __aeabi_f2d                          
00003a09  __extendsfdf2                        
00003a49  atoi                                 
00003b7d  I2C_OLED_Set_Pos                     
00003bb9  __aeabi_i2f                          
00003bb9  __floatsisf                          
00003bf5  __gesf2                              
00003bf5  __gtsf2                              
00003c31  __TI_auto_init_nobinit_nopinit       
00003c6d  __cmpsf2                             
00003c6d  __eqsf2                              
00003c6d  __lesf2                              
00003c6d  __ltsf2                              
00003c6d  __nesf2                              
00003ca9  __muldsi3                            
00003d4d  SYSCFG_DL_TIMER_0_init               
00003d81  _IQ24toF                             
00003de1  _IQ24mpy                             
00003e39  __aeabi_i2d                          
00003e39  __floatsidf                          
00003e65  vsprintf                             
00003e91  PID_Init                             
00003f5d  SysTick_Increasment                  
00003f85  _c_int00_noargs                      
00003fd3  DL_I2C_setClockConfig                
00003ff9  __aeabi_ui2d                         
00003ff9  __floatunsidf                        
0000401d  __aeabi_lmul                         
0000401d  __muldi3                             
00004041  PID_SetParams                        
00004063  memccpy                              
000040a5  Delay                                
000040c5  main                                 
000040e5  __aeabi_llsl                         
000040e5  __ashldi3                            
0000428d  DL_Timer_setCaptCompUpdateMethod     
000042a9  DL_Timer_setClockConfig              
000042c5  TIMA0_IRQHandler                     
00004569  DL_Timer_setCaptureCompareOutCtl     
00004621  __TI_zero_init_nomemset              
000046d9  __aeabi_uldivmod                     
00004713  TI_memcpy_small                      
00004725  __TI_decompress_none                 
00004749  DL_Timer_setCaptureCompareValue      
00004759  SYSCFG_DL_SYSTICK_init               
00004769  wcslen                               
00004779  __aeabi_memset                       
00004779  __aeabi_memset4                      
00004779  __aeabi_memset8                      
00004795  TI_memset_small                      
000047a5  Sys_GetTick                          
000047b1  DL_Common_delayCycles                
000047e9  SysTick_Handler                      
000047f1  __aeabi_errno_addr                   
000047f9  __aeabi_memcpy                       
000047f9  __aeabi_memcpy4                      
000047f9  __aeabi_memcpy8                      
00004801  abort                                
00004807  ADC0_IRQHandler                      
00004807  ADC1_IRQHandler                      
00004807  AES_IRQHandler                       
00004807  CANFD0_IRQHandler                    
00004807  DAC0_IRQHandler                      
00004807  DMA_IRQHandler                       
00004807  Default_Handler                      
00004807  GROUP0_IRQHandler                    
00004807  HardFault_Handler                    
00004807  I2C0_IRQHandler                      
00004807  I2C1_IRQHandler                      
00004807  NMI_Handler                          
00004807  PendSV_Handler                       
00004807  RTC_IRQHandler                       
00004807  SPI0_IRQHandler                      
00004807  SPI1_IRQHandler                      
00004807  SVC_Handler                          
00004807  TIMA1_IRQHandler                     
00004807  TIMG0_IRQHandler                     
00004807  TIMG12_IRQHandler                    
00004807  TIMG6_IRQHandler                     
00004807  TIMG7_IRQHandler                     
00004807  TIMG8_IRQHandler                     
00004807  UART0_IRQHandler                     
00004807  UART1_IRQHandler                     
00004807  UART2_IRQHandler                     
00004807  UART3_IRQHandler                     
0000480a  C$$EXIT                              
0000480b  HOSTexit                             
0000480f  Reset_Handler                        
00004825  _system_pre_init                     
00004830  asc2_1608                            
00004e20  asc2_0806                            
00005050  __aeabi_ctype_table_                 
00005050  __aeabi_ctype_table_C                
00005151  _IQ6div_lookup                       
00005284  __TI_Handler_Table_Base              
00005290  __TI_Handler_Table_Limit             
00005298  __TI_CINIT_Base                      
000052a8  __TI_CINIT_Limit                     
000052a8  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  _sys_memory                          
202004f0  gTIMER_0Backup                       
202005ac  gMotorAFrontBackup                   
2020064c  gMotorBFrontBackup                   
202006ec  tick                                 
202006f4  ExISR_Flag                           
202006f8  Cycle                                
202006f9  set_Cycle                            
202006fc  Motor_Font_Left                      
20200744  Motor_Font_Right                     
2020078c  Motor                                
20200794  Data_Tracker_Input                   
2020079c  Data_MotorEncoder                    
202007a0  Data_Motor_TarSpeed                  
202007a4  Data_Tracker_Offset                  
202007a8  __aeabi_errno                        
202007ac  delayTick                            
202007b4  uwTick                               
202007b8  Motor_Flag                           
202007ba  is_turning                           
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[234 symbols]
