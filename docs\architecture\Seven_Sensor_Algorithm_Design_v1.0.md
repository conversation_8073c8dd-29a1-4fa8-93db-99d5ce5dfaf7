# 七路循迹传感器修正算法架构设计

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-08-01 |
| 负责人 | Bob (架构师) |
| 项目名称 | TI_CAR1.9 七路循迹传感器修正算法架构设计 |
| 文档状态 | 技术设计完成 |

## 2. 架构概述

### 2.1 设计原则
- **渐进式修正**: 根据偏差大小分级调整修正强度
- **参数可配置**: 支持运行时参数调整
- **向后兼容**: 保持与现有系统的完全兼容
- **实时性保证**: 确保算法在20ms周期内完成

### 2.2 核心改进点
1. **自适应修正系数**: 替换固定INDEX=1.5为分级修正
2. **优化权重计算**: 修正传感器位置权重计算错误
3. **智能滤波**: 根据偏差变化动态调整滤波参数
4. **参数管理**: 统一管理所有调试参数

## 3. 算法设计

### 3.1 分级修正算法

#### 3.1.1 修正系数定义
```c
// 修正参数结构体
typedef struct {
    float index_small;   // 小偏差修正系数 (推荐: 0.6)
    float index_medium;  // 中等偏差修正系数 (推荐: 0.8)  
    float index_large;   // 大偏差修正系数 (推荐: 1.0)
    _iq threshold_small; // 小偏差阈值 (推荐: 1.0cm)
    _iq threshold_medium;// 中等偏差阈值 (推荐: 2.5cm)
    _iq filter_fast;     // 快速滤波系数 (推荐: 0.5)
    _iq filter_slow;     // 慢速滤波系数 (推荐: 0.8)
} TrackerParams_t;

// 全局参数实例
extern TrackerParams_t g_tracker_params;
```

#### 3.1.2 自适应修正函数
```c
/**
 * @brief 计算自适应修正值
 * @param offset 当前偏差值
 * @return 修正后的转向调整值
 */
_iq Calculate_Adaptive_Correction(_iq offset) {
    _iq abs_offset = _IQabs(offset);
    _iq correction_factor;
    
    // 分级修正策略
    if (abs_offset <= g_tracker_params.threshold_small) {
        correction_factor = _IQ(g_tracker_params.index_small);
    } else if (abs_offset <= g_tracker_params.threshold_medium) {
        correction_factor = _IQ(g_tracker_params.index_medium);
    } else {
        correction_factor = _IQ(g_tracker_params.index_large);
    }
    
    return _IQmpy(offset, correction_factor);
}
```

### 3.2 权重计算优化

#### 3.2.1 当前问题分析
```c
// 当前实现 (存在精度问题)
_iq sensor_pos = _IQmpy(_IQ(i - 3.0f), DIS_INRERVAL);
```

#### 3.2.2 优化实现
```c
/**
 * @brief 优化的传感器位置权重计算
 * @param sensor_index 传感器索引 (0-6)
 * @return 传感器位置权重 (cm)
 */
_iq Calculate_Sensor_Position(_iq sensor_index) {
    // 确保中心传感器(索引3)权重为0
    // 位置: -4.5, -3.0, -1.5, 0, 1.5, 3.0, 4.5 cm
    return _IQmpy(_IQ(sensor_index - 3), DIS_INRERVAL);
}
```

### 3.3 自适应滤波算法

#### 3.3.1 滤波策略
```c
/**
 * @brief 计算自适应滤波系数
 * @param current_offset 当前偏差
 * @param last_offset 上次偏差
 * @return 滤波系数
 */
_iq Calculate_Adaptive_Filter(_iq current_offset, _iq last_offset) {
    _iq offset_change = _IQabs(current_offset - last_offset);
    
    // 偏差变化大时使用快速滤波，变化小时使用慢速滤波
    if (offset_change > _IQ(2.0)) {
        return g_tracker_params.filter_fast;  // 快速响应
    } else {
        return g_tracker_params.filter_slow;  // 平滑处理
    }
}
```

## 4. 代码实现方案

### 4.1 文件修改清单

#### 4.1.1 BSP/Inc/Tracker.h
```c
// 新增参数结构体和函数声明
typedef struct {
    float index_small;
    float index_medium;
    float index_large;
    _iq threshold_small;
    _iq threshold_medium;
    _iq filter_fast;
    _iq filter_slow;
} TrackerParams_t;

// 新增函数声明
_iq Calculate_Adaptive_Correction(_iq offset);
_iq Calculate_Adaptive_Filter(_iq current_offset, _iq last_offset);
void Tracker_Params_Init(void);
void Tracker_Params_Set(TrackerParams_t *params);
TrackerParams_t* Tracker_Params_Get(void);
```

#### 4.1.2 BSP/Src/Tracker.c 修改点
1. **权重计算修正** (第69行)
2. **新增自适应算法函数**
3. **参数管理函数实现**

#### 4.1.3 APP/Src/Task_App.c 修改点
1. **修正算法调用** (第123行)
2. **自适应滤波应用** (第162行)
3. **参数初始化调用**

### 4.2 性能分析

#### 4.2.1 计算复杂度
- **原算法**: 1次乘法运算
- **新算法**: 2-3次比较 + 1次乘法运算
- **性能影响**: 微乎其微 (<1% CPU占用增加)

#### 4.2.2 内存占用
- **参数结构体**: 28字节 (7个float/IQ值)
- **临时变量**: 12字节
- **总增加**: 40字节 (可忽略)

## 5. 参数配置方案

### 5.1 默认参数配置
```c
// 推荐的默认参数
static const TrackerParams_t DEFAULT_PARAMS = {
    .index_small = 0.6f,           // 小偏差修正系数
    .index_medium = 0.8f,          // 中等偏差修正系数
    .index_large = 1.0f,           // 大偏差修正系数
    .threshold_small = _IQ(1.0),   // 小偏差阈值 1cm
    .threshold_medium = _IQ(2.5),  // 中等偏差阈值 2.5cm
    .filter_fast = _IQ(0.5),       // 快速滤波系数
    .filter_slow = _IQ(0.8)        // 慢速滤波系数
};
```

### 5.2 参数调试接口
```c
// 通过按键调整参数的示例
void Debug_Adjust_Params(uint8_t key_val) {
    TrackerParams_t *params = Tracker_Params_Get();
    
    switch(key_val) {
        case 1: // 减小修正系数
            params->index_small = fmax(0.1f, params->index_small - 0.1f);
            break;
        case 2: // 增大修正系数  
            params->index_small = fmin(2.0f, params->index_small + 0.1f);
            break;
        // 其他参数调整...
    }
}
```

## 6. 测试验证方案

### 6.1 单元测试
- **权重计算测试**: 验证7个传感器位置权重正确性
- **修正算法测试**: 验证不同偏差下的修正系数
- **滤波算法测试**: 验证滤波效果

### 6.2 集成测试
- **循迹精度测试**: 对比优化前后的循迹精度
- **稳定性测试**: 测量左右摆动幅度
- **响应性测试**: 测试对突然偏差的响应时间

### 6.3 性能测试
- **实时性测试**: 确保20ms周期内完成计算
- **内存使用测试**: 验证内存占用在可接受范围
- **参数调试测试**: 验证参数调整功能正常

## 7. 风险评估与缓解

### 7.1 技术风险
- **风险**: 算法复杂度增加影响实时性
- **缓解**: 优化算法实现，使用查表法替代复杂计算

### 7.2 兼容性风险  
- **风险**: 新算法与现有系统不兼容
- **缓解**: 保留原算法作为备选，提供切换机制

### 7.3 调试风险
- **风险**: 参数调整错误导致循迹失效
- **缓解**: 提供参数重置功能，设置安全边界

## 8. 实施计划

### 8.1 开发优先级
1. **P0**: 核心算法实现 (自适应修正)
2. **P1**: 权重计算优化
3. **P2**: 自适应滤波
4. **P3**: 参数管理系统

### 8.2 验证里程碑
- **里程碑1**: 算法功能验证完成
- **里程碑2**: 性能测试通过
- **里程碑3**: 集成测试通过
- **里程碑4**: 现场测试验证完成
