# PRD - 传感器校验用户交互需求分析

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-07-31 |
| 负责人 | Emma (产品经理) |
| 项目名称 | TI_CAR1.4 传感器校验封装设计 |
| 文档状态 | 用户交互需求分析完成 |

## 2. 背景与问题陈述

### 2.1 问题描述
当前传感器校验流程使用阻塞式的`while`循环，导致整个系统在校验期间无法响应其他任务，需要将其改造为非阻塞的状态机模式，完美融合到任务调度框架中。

### 2.2 当前实现分析

#### 2.2.1 传感器校验流程
```c
// 当前阻塞式实现
void HD_Init(void) {
    No_MCU_Ganv_Sensor_Init_Frist(&sensor);
    No_Mcu_Ganv_Sensor_Task_Without_tick(&sensor);
    Get_Anolog_Value(&sensor, Analog);

    while(HD_count != 3) {  // ⚠️ 阻塞整个系统
        // 校验逻辑...
    }
    
    No_MCU_Ganv_Sensor_Init(&sensor, white, black);
}
```

#### 2.2.2 校验状态分析
| HD_count | 状态描述 | 用户操作 | 系统行为 |
|----------|----------|----------|----------|
| 0 | 初始状态 | 观察当前值 | 显示实时模拟值 |
| 1 | 白色校验 | 按KEY2确认 | 采集白色基准值 |
| 2 | 黑色校验 | 按KEY2确认 | 采集黑色基准值 |
| 3 | 校验完成 | 自动退出 | 完成传感器初始化 |

#### 2.2.3 用户交互流程
1. **准备阶段**: 用户将传感器放置在中性位置
2. **白色校验**: 用户将传感器放在白色区域，按KEY2确认
3. **黑色校验**: 用户将传感器放在黑色区域，按KEY2确认
4. **完成校验**: 系统自动完成初始化

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **非阻塞校验**: 将校验流程改为状态机，不阻塞系统
2. **用户体验优化**: 提供清晰的校验指导和状态反馈
3. **框架集成**: 完美融合到现有任务调度框架

### 3.2 关键结果 (Key Results)
1. **系统响应性**: 校验期间系统其他功能正常运行
2. **校验准确性**: 校验结果与原实现完全一致
3. **用户友好性**: 提供清晰的状态提示和操作指导
4. **代码复用性**: 校验模块可独立使用和测试

### 3.3 反向指标 (Counter Metrics)
1. **复杂度控制**: 不显著增加代码复杂度
2. **内存占用**: 状态机内存开销 < 200字节
3. **响应延迟**: 按键响应延迟 < 100ms

## 4. 用户画像与用户故事

### 4.1 目标用户
- **操作人员**: 需要进行传感器校验的技术人员
- **开发人员**: 需要集成传感器校验功能的开发者
- **测试人员**: 需要验证校验功能的测试工程师

### 4.2 用户故事
- **作为操作人员**, 我希望校验过程有清晰的提示，这样我就能正确完成校验操作
- **作为开发人员**, 我希望校验模块易于集成，这样我就能快速添加到项目中
- **作为测试人员**, 我希望校验过程可重复，这样我就能进行可靠的功能测试

## 5. 功能规格详述

### 5.1 用户交互设计

#### 5.1.1 校验状态机设计
```c
typedef enum {
    SENSOR_CAL_IDLE,        // 空闲状态
    SENSOR_CAL_INIT,        // 初始化状态
    SENSOR_CAL_WAIT_WHITE,  // 等待白色校验
    SENSOR_CAL_WHITE,       // 白色校验中
    SENSOR_CAL_WAIT_BLACK,  // 等待黑色校验
    SENSOR_CAL_BLACK,       // 黑色校验中
    SENSOR_CAL_COMPLETE,    // 校验完成
    SENSOR_CAL_ERROR        // 校验错误
} SensorCalState_t;
```

#### 5.1.2 用户界面设计
**OLED显示内容**:
```
状态0: "Sensor Ready"
       "Press KEY2 to start"
       
状态1: "Place on WHITE"
       "Press KEY2 to cal"
       "Val: 1800 1850 1900..."
       
状态2: "Place on BLACK" 
       "Press KEY2 to cal"
       "Val: 300 350 280..."
       
状态3: "Calibration OK"
       "System Ready"
```

#### 5.1.3 LED状态指示
- **LED1**: 校验进行中（闪烁）
- **LED2**: 白色校验完成（常亮）
- **LED3**: 黑色校验完成（常亮）
- **LED4**: 校验错误（快闪）

### 5.2 状态转换逻辑

#### 5.2.1 状态转换图
```
IDLE → [启动校验] → INIT → WAIT_WHITE
                              ↓ [KEY2按下]
COMPLETE ← [校验完成] ← BLACK ← WHITE ← [KEY2按下]
                              ↓ [KEY2按下]
                         WAIT_BLACK
```

#### 5.2.2 事件处理
| 当前状态 | 触发事件 | 下一状态 | 动作 |
|----------|----------|----------|------|
| IDLE | 启动校验 | INIT | 初始化传感器 |
| INIT | 初始化完成 | WAIT_WHITE | 显示白色校验提示 |
| WAIT_WHITE | KEY2按下 | WHITE | 采集白色基准值 |
| WHITE | 采集完成 | WAIT_BLACK | 显示黑色校验提示 |
| WAIT_BLACK | KEY2按下 | BLACK | 采集黑色基准值 |
| BLACK | 采集完成 | COMPLETE | 完成校验初始化 |

### 5.3 技术实现方案

#### 5.3.1 状态机结构体设计
```c
typedef struct {
    SensorCalState_t state;           // 当前状态
    SensorCalState_t last_state;      // 上一状态
    uint32_t state_enter_time;        // 状态进入时间
    uint32_t timeout_ms;              // 状态超时时间
    bool key_pressed;                 // 按键按下标志
    uint8_t retry_count;              // 重试次数
    void (*callback)(bool success);   // 完成回调函数
    
    // 传感器相关
    No_MCU_Sensor *sensor;            // 传感器实例
    unsigned short white_values[8];   // 白色校验值
    unsigned short black_values[8];   // 黑色校验值
    unsigned short current_values[8]; // 当前采样值
} SensorCalibration_t;
```

#### 5.3.2 任务集成方案
```c
// 在Task_App.c中添加校验任务
void Task_SensorCalibration(void *para) {
    SensorCalibration_StateMachine();
    SensorCalibration_UpdateDisplay();
}

// 修改按键任务，添加校验按键处理
void Task_Key(void *para) {
    // 原有按键逻辑...
    
    // 校验按键处理
    if (SensorCalibration_IsActive()) {
        SensorCalibration_HandleKey(Key_Val);
    }
}

// 修改OLED任务，添加校验显示
void Task_OLED(void *para) {
    if (SensorCalibration_IsActive()) {
        SensorCalibration_Display();
    } else {
        // 原有显示逻辑...
    }
}
```

#### 5.3.3 API接口设计
```c
// 校验控制接口
bool SensorCalibration_Start(No_MCU_Sensor *sensor, void (*callback)(bool));
bool SensorCalibration_Stop(void);
bool SensorCalibration_IsActive(void);
SensorCalState_t SensorCalibration_GetState(void);

// 事件处理接口
void SensorCalibration_HandleKey(uint8_t key_val);
void SensorCalibration_StateMachine(void);
void SensorCalibration_UpdateDisplay(void);

// 状态查询接口
bool SensorCalibration_IsComplete(void);
bool SensorCalibration_HasError(void);
void SensorCalibration_GetResults(unsigned short *white, unsigned short *black);
```

## 6. 用户体验优化

### 6.1 操作指导优化
1. **清晰的提示信息**: 每个状态都有明确的操作指导
2. **实时数值显示**: 显示当前传感器读数，帮助用户判断
3. **状态进度指示**: 显示当前校验进度（1/2, 2/2）
4. **错误处理**: 提供重试机制和错误提示

### 6.2 交互流程优化
1. **非强制启动**: 校验可选择性启动，不影响正常使用
2. **中途退出**: 支持校验过程中退出（按其他按键）
3. **超时保护**: 长时间无操作自动退出校验模式
4. **数据验证**: 校验数据合理性检查

### 6.3 反馈机制优化
1. **视觉反馈**: LED状态指示 + OLED文字提示
2. **声音反馈**: 可选的蜂鸣器提示（如果有硬件）
3. **触觉反馈**: 按键确认的即时响应
4. **状态持久化**: 校验结果保存，避免重复校验

## 7. 集成方案设计

### 7.1 任务调度集成
```c
void Task_Init(void) {
    Motor_Start();
    OLED_Init();
    Interrupt_Init();
    
    // 添加传感器校验任务
    Task_Add("SensorCal", Task_SensorCalibration, 50, NULL, 4);
    
    Task_Add("Motor", Task_Motor_PID, 20, NULL, 0);
    Task_Add("Tracker", Task_Tracker, 10, NULL, 1);
    Task_Add("Key", Task_Key, 20, NULL, 3);
    Task_Add("OLED", Task_OLED, 30, NULL, 2);
}
```

### 7.2 启动方式设计
```c
// 方式1: 开机自动启动校验
void Auto_Start_Calibration(void) {
    if (!SensorCalibration_IsComplete()) {
        SensorCalibration_Start(&sensor, Calibration_Complete_Callback);
    }
}

// 方式2: 按键组合启动校验
void Manual_Start_Calibration(void) {
    if (Key_Val == KEY_COMBINATION) {  // 如KEY2+KEY3同时按下
        SensorCalibration_Start(&sensor, Calibration_Complete_Callback);
    }
}

// 方式3: 条件触发启动校验
void Conditional_Start_Calibration(void) {
    if (sensor_data_invalid || force_recalibration) {
        SensorCalibration_Start(&sensor, Calibration_Complete_Callback);
    }
}
```

### 7.3 回调处理设计
```c
void Calibration_Complete_Callback(bool success) {
    if (success) {
        // 校验成功，启动正常功能
        SensorCalibration_GetResults(white, black);
        No_MCU_Ganv_Sensor_Init(&sensor, white, black);
        
        // 启动循迹任务
        Task_Add("Tracker", Task_Tracker, 10, NULL, 1);
        
        // 更新系统状态
        system_ready = true;
    } else {
        // 校验失败，进入错误处理
        system_error = true;
        // 可选择重试或使用默认值
    }
}
```

## 8. 实施优先级

### 8.1 核心功能 (高优先级)
1. **状态机基础框架**: 实现基本的状态转换逻辑
2. **按键事件处理**: 集成到现有按键任务中
3. **OLED显示集成**: 添加校验状态显示
4. **传感器接口封装**: 封装传感器操作接口

### 8.2 用户体验 (中优先级)
1. **LED状态指示**: 添加视觉反馈
2. **超时保护机制**: 防止长时间阻塞
3. **错误处理和重试**: 提升鲁棒性
4. **数据验证**: 确保校验数据有效性

### 8.3 高级功能 (低优先级)
1. **数据持久化**: 保存校验结果到Flash
2. **多传感器支持**: 扩展支持多个传感器
3. **远程校验**: 通过串口或无线进行校验
4. **自动校验**: 智能检测校验需求

## 9. 验收标准

### 9.1 功能验收
- [ ] 校验过程不阻塞系统其他功能
- [ ] 校验结果与原实现完全一致
- [ ] 支持校验过程中途退出
- [ ] 提供清晰的用户操作指导

### 9.2 性能验收
- [ ] 按键响应时间 < 100ms
- [ ] 状态机内存占用 < 200字节
- [ ] 校验完成时间 < 30秒
- [ ] 系统CPU占用增加 < 5%

### 9.3 用户体验验收
- [ ] 操作流程直观易懂
- [ ] 错误提示清晰明确
- [ ] 支持重复校验操作
- [ ] 校验状态实时反馈

## 10. 版本历史

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| v1.0 | 2025-07-31 | 初版创建，完成用户交互需求分析 | Emma |

---

**文档状态**: ✅ 用户交互需求分析完成  
**关键设计**: 状态机 + 任务集成 + 用户友好界面  
**推荐方案**: 非阻塞状态机 + 清晰的用户指导 + 完整的错误处理  
**下一步**: 进入状态机架构和任务集成设计阶段
