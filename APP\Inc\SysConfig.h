#ifndef __SysConfig_h
#define __SysConfig_h

/*MACRO*/
#define DEBUG            //调试模式必须保留 非调试可以注释
#define MOTOR_FULL_VALUE 260 //电机转一圈的编码值

/*STD C*/
#include <stdint.h>
#include <stdarg.h>
#include <string.h>
#include <stdlib.h>
#include <stdbool.h>
#include <stdio.h>
#include <math.h>

/*TI CCS*/
#include "ti_msp_dl_config.h"
#include "ti/iqmath/include/IQmathLib.h"

/*BSP*/
#include "PID.h"
#include "Task.h"
#include "Key_Led.h"
#include "SysTick.h"
#include "Motor.h"
#include "Tracker.h"
#include "OLED.h"
#include "No_Mcu_Ganv_Grayscale_Sensor_Config.h"
#include "ADC.h"


/*APP*/
#include "Task_App.h"
#include "Interrupt.h"

#endif
