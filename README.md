## TI_CAR1.4 智能小车项目

基于TI MSPM0G3507微控制器的智能小车控制系统。
本项目实现了电机控制、循迹功能、摄像头通讯、OLED显示等功能。

### 项目特性
- **微控制器**: TI MSPM0G3507 (ARM Cortex-M0+)
- **电机控制**: 双电机PID控制系统
- **循迹功能**: 基于传感器的路径跟踪
- **摄像头通讯**: 高效的UART+DMA串口通讯
- **显示系统**: OLED实时状态显示
- **任务调度**: 实时多任务调度系统

### 最新更新 (2025-07-30)
- ✅ 移除WIT角度传感器模块
- ✅ 激活摄像头通讯功能
- ✅ UART0资源重新分配
- ✅ **修正电机引脚配置** - 完成左右电机引脚分配修正
- ✅ **DRV8871控制优化** - 优化双PWM控制逻辑
- ✅ 优化系统性能和稳定性

## 系统架构

### 硬件模块
- **电机驱动**: DRV8871双PWM控制双电机，支持正反转和制动
- **编码器反馈**: 正交编码器实时速度检测
- **循迹传感器**: 多路红外传感器阵列
- **摄像头模块**: K230摄像头UART串口通讯，DMA数据传输
- **OLED显示**: I2C接口，实时状态显示
- **按键输入**: GPIO按键检测

### 软件架构
- **BSP层**: 硬件抽象层，驱动程序
- **APP层**: 应用逻辑层，任务实现
- **任务调度**: 基于优先级的实时任务调度

## 外设与引脚分配

### 电机控制系统 (已修正)
| 电机 | 功能 | 引脚 | 定时器/通道 | 说明 |
| --- | --- | --- | --- | --- |
| 左电机 | 编码器A | PB9 | GPIO中断 | 正交编码器A相 |
| 左电机 | 编码器B | PB8 | GPIO输入 | 正交编码器B相 |
| 左电机 | PWM IN1 | PB2 | TIMA1/CC_0 | DRV8871 IN1控制 |
| 左电机 | PWM IN2 | PB3 | TIMA1/CC_1 | DRV8871 IN2控制 |
| 右电机 | 编码器A | PB11 | GPIO中断 | 正交编码器A相 |
| 右电机 | 编码器B | PB10 | GPIO输入 | 正交编码器B相 |
| 右电机 | PWM IN1 | PB16 | TIMG7/CC_1 | DRV8871 IN1控制 |
| 右电机 | PWM IN2 | PB15 | TIMG7/CC_0 | DRV8871 IN2控制 |

### 通讯与传感器
| 外设 | 引脚 | 功能 | 说明 |
| --- | --- | --- | --- |
| UART0 | PA11 | 摄像头RX | K230摄像头，115200波特率，DMA传输 |
| UART0 | PA10 | 摄像头TX | 串口通讯 |
| I2C0 | PB0 | 灰度传感器SCL | I2C时钟线 |
| I2C0 | PB1 | 灰度传感器SDA | I2C数据线 |
| GPIO | PA23 | 左电机方向 | 方向指示输出 |
| GPIO | PA24 | 右电机方向 | 方向指示输出 |

### 调试接口
| 外设 | 引脚 | 功能 | 说明 |
| --- | --- | --- | --- |
| DEBUGSS | PA20 | Debug Clock | SWD调试接口 |
| DEBUGSS | PA19 | Debug Data | SWD调试接口 |

## 电机控制系统详解

### DRV8871双PWM控制模式
本项目使用DRV8871电机驱动芯片，采用双PWM控制模式：

| IN1 | IN2 | 电机状态 | 描述 |
| --- | --- | --- | --- |
| 0 | 0 | 滑行/制动 | 电机自由转动 |
| 0 | 1 | 正转 | 电机正向旋转 |
| 1 | 0 | 反转 | 电机反向旋转 |
| 1 | 1 | 制动 | 高侧衰减制动 |

### 编码器反馈系统
- **编码器类型**: 正交编码器，A/B两相
- **中断处理**: A相上升沿触发中断，B相判断方向
- **计数精度**: 支持正反转计数，实时速度反馈
- **数据存储**: `Data_MotorEncoder[0]`(右电机), `Data_MotorEncoder[1]`(左电机)

### PID控制系统
- **控制算法**: 增量式PID控制
- **反馈信号**: 编码器计数值
- **控制目标**: 电机转速闭环控制
- **参数调节**: Kp=2.0, Ki=1.0, Kd=0.15 (可调)

### 配置验证功能
项目提供了完整的电机配置验证功能：
```c
Motor_Configuration_Verify();  // 验证引脚配置正确性
Motor_Direction_Test();        // 测试电机方向一致性
Motor_PWM_Response_Test();     // 测试PWM响应特性
```

## BoosterPacks, Board Resources & Jumper Settings

Visit [LP_MSPM0G3507](https://www.ti.com/tool/LP-MSPM0G3507) for LaunchPad information, including user guide and hardware files.

| Pin | Peripheral | Function | LaunchPad Pin | LaunchPad Settings |
| --- | --- | --- | --- | --- |
| PA20 | DEBUGSS | SWCLK | N/A | <ul><li>PA20 is used by SWD during debugging<br><ul><li>`J101 15:16 ON` Connect to XDS-110 SWCLK while debugging<br><li>`J101 15:16 OFF` Disconnect from XDS-110 SWCLK if using pin in application</ul></ul> |
| PA19 | DEBUGSS | SWDIO | N/A | <ul><li>PA19 is used by SWD during debugging<br><ul><li>`J101 13:14 ON` Connect to XDS-110 SWDIO while debugging<br><li>`J101 13:14 OFF` Disconnect from XDS-110 SWDIO if using pin in application</ul></ul> |

### Device Migration Recommendations
This project was developed for a superset device included in the LP_MSPM0G3507 LaunchPad. Please
visit the [CCS User's Guide](https://software-dl.ti.com/msp430/esd/MSPM0-SDK/latest/docs/english/tools/ccs_ide_guide/doc_guide/doc_guide-srcs/ccs_ide_guide.html#sysconfig-project-migration)
for information about migrating to other MSPM0 devices.

### Low-Power Recommendations
TI recommends to terminate unused pins by setting the corresponding functions to
GPIO and configure the pins to output low or input with internal
pullup/pulldown resistor.

SysConfig allows developers to easily configure unused pins by selecting **Board**→**Configure Unused Pins**.

For more information about jumper configuration to achieve low-power using the
MSPM0 LaunchPad, please visit the [LP-MSPM0G3507 User's Guide](https://www.ti.com/lit/slau873).

## Example Usage

Compile, load and run the example.
