******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 10:05:53 2025

OUTPUT FILE NAME:   <TI_CAR1.6.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00004385


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005778  0001a888  R  X
  SRAM                  20200000   00008000  00000a84  0000757c  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005778   00005778    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004c50   00004c50    r-x .text
  00004d10    00004d10    00000a00   00000a00    r-- .rodata
  00005710    00005710    00000068   00000068    r-- .cinit
20200000    20200000    00000886   00000000    rw-
  20200000    20200000    00000400   00000000    rw- .sysmem
  20200400    20200400    000003a2   00000000    rw- .bss
  202007a4    202007a4    000000e2   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004c50     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000220            : _printfi.c.obj (.text._pconv_a)
                  00000cb0    000001dc            : _printfi.c.obj (.text._pconv_g)
                  00000e8c    000001b0     Task.o (.text.Task_Start)
                  0000103c    00000198     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000011d4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001366    00000002     Task.o (.text.Task_IdleFunction)
                  00001368    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  000014f0    00000144     PID.o (.text.PID_SProsc)
                  00001634    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001770    00000134            : qsort.c.obj (.text.qsort)
                  000018a4    00000130     OLED.o (.text.OLED_ShowChar)
                  000019d4    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001af4    00000110     OLED.o (.text.OLED_Init)
                  00001c04    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001d10    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001e14    000000f0     Task_App.o (.text.HD_Init)
                  00001f04    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001fec    000000e8     Task_App.o (.text.Task_Motor_PID)
                  000020d4    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000021b8    000000e0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00002298    000000e0     Task_App.o (.text.Task_Key)
                  00002378    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002454    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  0000252c    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00002604    000000d4     Motor.o (.text.Motor_SetDuty)
                  000026d8    000000b4     Task.o (.text.Task_Add)
                  0000278c    000000b0     Interrupt.o (.text.GROUP1_IRQHandler)
                  0000283c    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  000028e6    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000028e8    000000a2                            : udivmoddi4.S.obj (.text)
                  0000298a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  0000298c    0000009c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002a28    0000009c     Task_App.o (.text.Task_OLED)
                  00002ac4    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00002b5c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorAFront_init)
                  00002be8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBFront_init)
                  00002c74    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002d00    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002d84    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002e00    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002e74    0000000c     SysTick.o (.text.Sys_GetTick)
                  00002e80    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00002ef4    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00002f66    00000002     --HOLE-- [fill = 0]
                  00002f68    00000070     Task_App.o (.text.Task_GraySensor)
                  00002fd8    0000006e     OLED.o (.text.OLED_ShowString)
                  00003046    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  000030b2    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  0000311c    00000068     Motor.o (.text.Motor_Start)
                  00003184    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000031ec    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00003252    00000002     --HOLE-- [fill = 0]
                  00003254    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  000032b8    00000064     Key_Led.o (.text.Key_Read)
                  0000331c    00000064     Task_App.o (.text.Task_Init)
                  00003380    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000033e2    00000002     --HOLE-- [fill = 0]
                  000033e4    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00003446    00000002     --HOLE-- [fill = 0]
                  00003448    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  000034a8    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00003508    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003566    00000002     --HOLE-- [fill = 0]
                  00003568    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000035c4    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00003620    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003678    00000058            : _printfi.c.obj (.text._pconv_f)
                  000036d0    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003726    00000002     --HOLE-- [fill = 0]
                  00003728    00000054     Motor.o (.text.CalculateDutyValue)
                  0000377c    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  000037d0    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00003824    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003876    00000002     --HOLE-- [fill = 0]
                  00003878    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  000038c8    00000050     Interrupt.o (.text.Interrupt_Init)
                  00003918    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00003968    00000050     ADC.o (.text.adc_getValue)
                  000039b8    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00003a04    0000004c     OLED.o (.text.OLED_Printf)
                  00003a50    0000004c     ADC.o (.text.wait_idle_with_timeout)
                  00003a9c    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00003ae6    00000002     --HOLE-- [fill = 0]
                  00003ae8    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00003b32    00000048     Motor.o (.text.Motor_GetSpeed)
                  00003b7a    00000002     --HOLE-- [fill = 0]
                  00003b7c    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003bc4    00000048     OLED.o (.text.mspm0_i2c_disable)
                  00003c0c    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00003c50    00000044     Motor.o (.text.SetPWMValue)
                  00003c94    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00003cd6    00000002     --HOLE-- [fill = 0]
                  00003cd8    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00003d1a    00000002     --HOLE-- [fill = 0]
                  00003d1c    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00003d5c    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003d9c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00003ddc    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00003e1c    0000003e     Task.o (.text.Task_CMP)
                  00003e5a    00000002     --HOLE-- [fill = 0]
                  00003e5c    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003e98    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003ed4    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00003f10    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00003f4c    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00003f88    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00003fc4    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00004000    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000403c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00004076    00000002     --HOLE-- [fill = 0]
                  00004078    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000040b2    00000002     --HOLE-- [fill = 0]
                  000040b4    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000040e8    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000411c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00004150    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00004180    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  000041b0    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000041e0    0000002c     iqmath.a : _IQNmpy.o (.text._IQ24mpy)
                  0000420c    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00004238    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00004264    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00004290    0000002a     PID.o (.text.PID_Init)
                  000042ba    00000028     OLED.o (.text.DL_Common_updateReg)
                  000042e2    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000430a    00000002     --HOLE-- [fill = 0]
                  0000430c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00004334    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  0000435c    00000028     SysTick.o (.text.SysTick_Increasment)
                  00004384    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000043ac    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000043d2    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000043f8    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  0000441c    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00004440    00000022     PID.o (.text.PID_SetParams)
                  00004462    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004484    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000044a4    00000020     SysTick.o (.text.Delay)
                  000044c4    00000020     main.o (.text.main)
                  000044e4    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00004502    00000002     --HOLE-- [fill = 0]
                  00004504    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00004522    00000002     --HOLE-- [fill = 0]
                  00004524    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  00004540    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  0000455c    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00004578    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00004594    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  000045b0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000045cc    0000001c     Interrupt.o (.text.DL_GPIO_enableInterrupt)
                  000045e8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00004604    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00004620    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  0000463c    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00004658    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00004674    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00004690    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000046ac    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000046c8    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  000046e4    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00004700    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0000471c    0000001c     Interrupt.o (.text.TIMA0_IRQHandler)
                  00004738    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00004750    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00004768    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00004780    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00004798    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000047b0    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000047c8    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  000047e0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000047f8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00004810    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00004828    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00004840    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00004858    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00004870    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00004888    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  000048a0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000048b8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000048d0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000048e8    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00004900    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00004918    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00004930    00000018     OLED.o (.text.DL_I2C_reset)
                  00004948    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00004960    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00004978    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00004990    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000049a8    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000049c0    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000049d8    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000049f0    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004a08    00000018     Interrupt.o (.text.DL_Timer_startCounter)
                  00004a20    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00004a38    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  00004a50    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  00004a66    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  00004a7c    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00004a92    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00004aa8    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  00004abe    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00004ad4    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00004aea    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00004afe    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00004b12    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  00004b26    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00004b3a    00000002     --HOLE-- [fill = 0]
                  00004b3c    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00004b50    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00004b64    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00004b78    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00004b8c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00004ba0    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00004bb4    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00004bc8    00000012     Interrupt.o (.text.DL_Timer_getPendingInterrupt)
                  00004bda    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00004bec    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00004bfe    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  00004c0e    00000002     --HOLE-- [fill = 0]
                  00004c10    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00004c20    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00004c30    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00004c40    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004c50    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00004c5e    00000002     --HOLE-- [fill = 0]
                  00004c60    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00004c6e    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00004c7c    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00004c8a    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00004c94    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004c9e    00000002     --HOLE-- [fill = 0]
                  00004ca0    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00004cb0    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004cba    0000000a            : vsprintf.c.obj (.text._outc)
                  00004cc4    00000008     Interrupt.o (.text.SysTick_Handler)
                  00004ccc    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00004cd4    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00004cdc    00000006     libc.a : exit.c.obj (.text:abort)
                  00004ce2    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00004ce6    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00004cea    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00004cee    00000002     --HOLE-- [fill = 0]
                  00004cf0    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00004d00    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00004d04    0000000c     --HOLE-- [fill = 0]

.cinit     0    00005710    00000068     
                  00005710    0000003d     (.cinit..data.load) [load image, compression = lzss]
                  0000574d    00000003     --HOLE-- [fill = 0]
                  00005750    0000000c     (__TI_handler_table)
                  0000575c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00005764    00000010     (__TI_cinit_table)
                  00005774    00000004     --HOLE-- [fill = 0]

.rodata    0    00004d10    00000a00     
                  00004d10    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00005300    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00005528    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00005530    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00005631    00000003     ti_msp_dl_config.o (.rodata.gMotorAFrontClockConfig)
                  00005634    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000565c    00000021     Task_App.o (.rodata.str1.492715258893803702.1)
                  0000567d    00000003     ti_msp_dl_config.o (.rodata.gMotorBFrontClockConfig)
                  00005680    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00005694    00000012     Task_App.o (.rodata.str1.11683036942922059812.1)
                  000056a6    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  000056b7    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  000056c8    0000000c     Task_App.o (.rodata.str1.16020955549137178199.1)
                  000056d4    0000000c     Task_App.o (.rodata.str1.8896853068034818020.1)
                  000056e0    0000000b     Task_App.o (.rodata.str1.10635198597896025474.1)
                  000056eb    00000001     --HOLE-- [fill = 0]
                  000056ec    00000008     ti_msp_dl_config.o (.rodata.gMotorAFrontConfig)
                  000056f4    00000008     ti_msp_dl_config.o (.rodata.gMotorBFrontConfig)
                  000056fc    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00005702    00000005     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00005707    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  0000570a    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  0000570c    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000400     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000003f0     --HOLE--

.bss       0    20200400    000003a2     UNINITIALIZED
                  20200400    000000f0     Task.o (.bss.Task_Schedule)
                  202004f0    000000bc     (.common:gTIMER_0Backup)
                  202005ac    00000004     (.common:ExISR_Flag)
                  202005b0    000000b0     (.common:sensor)
                  20200660    000000a0     (.common:gMotorAFrontBackup)
                  20200700    000000a0     (.common:gMotorBFrontBackup)
                  202007a0    00000001     Task_App.o (.bss.Task_Key.Key_Old)
                  202007a1    00000001     (.common:HD_count)

.data      0    202007a4    000000e2     UNINITIALIZED
                  202007a4    00000048     Motor.o (.data.Motor_Font_Left)
                  202007ec    00000048     Motor.o (.data.Motor_Font_Right)
                  20200834    00000010     Task_App.o (.data.Analog)
                  20200844    00000010     Task_App.o (.data.black)
                  20200854    00000010     Task_App.o (.data.white)
                  20200864    00000008     Task_App.o (.data.Motor)
                  2020086c    00000004     Task_App.o (.data.Data_MotorEncoder)
                  20200870    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  20200874    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  20200878    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  2020087c    00000004     SysTick.o (.data.delayTick)
                  20200880    00000004     SysTick.o (.data.uwTick)
                  20200884    00000001     Task_App.o (.data.Digtal)
                  20200885    00000001     Task.o (.data.Task_Num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2848    95        508    
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2888    287       508    
                                                                 
    .\APP\Src\
       Task_App.o                       1108    97        247    
       Interrupt.o                      508     0         4      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1616    97        251    
                                                                 
    .\BSP\Src\
       OLED_Font.o                      0       2072      0      
       OLED.o                           1858    0         0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1192    0         0      
       Task.o                           676     0         241    
       Motor.o                          564     0         144    
       PID.o                            400     0         0      
       ADC.o                            320     0         0      
       Key_Led.o                        122     0         0      
       SysTick.o                        84      0         8      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5216    2072      393    
                                                                 
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_i2c.o                         132     0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1182    0         0      
                                                                 
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/rts/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                        48      0         0      
       _IQNmpy.o                        44      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           92      0         0      
                                                                 
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       vsprintf.c.obj                   78      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5736    291       4      
                                                                 
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     418     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2746    0         0      
                                                                 
       Heap:                            0       0         1024   
       Stack:                           0       0         512    
       Linker Generated:                0       97        0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     19480   2844      2692   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00005764 records: 2, size/record: 8, table size: 16
	.data: load addr=00005710, load size=0000003d bytes, run addr=202007a4, run size=000000e2 bytes, compression=lzss
	.bss: load addr=0000575c, load size=00000008 bytes, run addr=20200400, run size=000003a2 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00005750 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000011d5     00004ca0     00004c9c   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00004385     00004cf0     00004cea   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00004ce3  ADC0_IRQHandler                      
00004ce3  ADC1_IRQHandler                      
00004ce3  AES_IRQHandler                       
20200834  Analog                               
00004ce6  C$$EXIT                              
00004ce3  CANFD0_IRQHandler                    
00004ce3  DAC0_IRQHandler                      
00003d1d  DL_ADC12_setClockConfig              
00004c8b  DL_Common_delayCycles                
00003509  DL_I2C_fillControllerTXFIFO          
000043d3  DL_I2C_setClockConfig                
00002379  DL_SYSCTL_configSYSPLL               
00003255  DL_SYSCTL_setHFCLKSourceHFXTParams   
00003c0d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001d11  DL_Timer_initFourCCPWMMode           
00001f05  DL_Timer_initTimerMode               
000046e5  DL_Timer_setCaptCompUpdateMethod     
000049f1  DL_Timer_setCaptureCompareOutCtl     
00004c21  DL_Timer_setCaptureCompareValue      
00004701  DL_Timer_setClockConfig              
00004ce3  DMA_IRQHandler                       
2020086c  Data_MotorEncoder                    
20200870  Data_Motor_TarSpeed                  
20200874  Data_Tracker_Offset                  
00004ce3  Default_Handler                      
000044a5  Delay                                
20200884  Digtal                               
202005ac  ExISR_Flag                           
00004ce3  GROUP0_IRQHandler                    
0000278d  GROUP1_IRQHandler                    
000021b9  Get_Analog_value                     
00003f11  Get_Anolog_Value                     
00004c51  Get_Digtal_For_User                  
00001e15  HD_Init                              
202007a1  HD_count                             
00004ce7  HOSTexit                             
00004ce3  HardFault_Handler                    
00004ce3  I2C0_IRQHandler                      
00004ce3  I2C1_IRQHandler                      
000030b3  I2C_OLED_Clear                       
00003f4d  I2C_OLED_Set_Pos                     
00002ac5  I2C_OLED_WR_Byte                     
00003449  I2C_OLED_i2c_sda_unlock              
000038c9  Interrupt_Init                       
000032b9  Key_Read                             
20200864  Motor                                
202007a4  Motor_Font_Left                      
202007ec  Motor_Font_Right                     
00003b33  Motor_GetSpeed                       
00002605  Motor_SetDuty                        
0000311d  Motor_Start                          
00004ce3  NMI_Handler                          
00001369  No_MCU_Ganv_Sensor_Init              
00002ef5  No_MCU_Ganv_Sensor_Init_Frist        
00003c95  No_Mcu_Ganv_Sensor_Task_Without_tick 
00001af5  OLED_Init                            
00003a05  OLED_Printf                          
000018a5  OLED_ShowChar                        
00002fd9  OLED_ShowString                      
00004291  PID_Init                             
000014f1  PID_SProsc                           
00004441  PID_SetParams                        
00004ce3  PendSV_Handler                       
00004ce3  RTC_IRQHandler                       
00004ceb  Reset_Handler                        
00004ce3  SPI0_IRQHandler                      
00004ce3  SPI1_IRQHandler                      
00004ce3  SVC_Handler                          
0000377d  SYSCFG_DL_ADC1_init                  
0000103d  SYSCFG_DL_GPIO_init                  
000034a9  SYSCFG_DL_I2C_OLED_init              
00002b5d  SYSCFG_DL_MotorAFront_init           
00002be9  SYSCFG_DL_MotorBFront_init           
000037d1  SYSCFG_DL_SYSCTL_init                
00004c31  SYSCFG_DL_SYSTICK_init               
0000411d  SYSCFG_DL_TIMER_0_init               
00003b7d  SYSCFG_DL_init                       
0000298d  SYSCFG_DL_initPower                  
00004cc5  SysTick_Handler                      
0000435d  SysTick_Increasment                  
00002e75  Sys_GetTick                          
0000471d  TIMA0_IRQHandler                     
00004ce3  TIMA1_IRQHandler                     
00004ce3  TIMG0_IRQHandler                     
00004ce3  TIMG12_IRQHandler                    
00004ce3  TIMG6_IRQHandler                     
00004ce3  TIMG7_IRQHandler                     
00004ce3  TIMG8_IRQHandler                     
00004bdb  TI_memcpy_small                      
00004c7d  TI_memset_small                      
000026d9  Task_Add                             
00002f69  Task_GraySensor                      
00001367  Task_IdleFunction                    
0000331d  Task_Init                            
00002299  Task_Key                             
00001fed  Task_Motor_PID                       
00002a29  Task_OLED                            
00000e8d  Task_Start                           
00004ce3  UART0_IRQHandler                     
00004ce3  UART1_IRQHandler                     
00004ce3  UART2_IRQHandler                     
00004ce3  UART3_IRQHandler                     
000041e1  _IQ24mpy                             
00004181  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00005764  __TI_CINIT_Base                      
00005774  __TI_CINIT_Limit                     
00005774  __TI_CINIT_Warm                      
00005750  __TI_Handler_Table_Base              
0000575c  __TI_Handler_Table_Limit             
00004001  __TI_auto_init_nobinit_nopinit       
00002d85  __TI_decompress_lzss                 
00004bed  __TI_decompress_none                 
00003621  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00004ad5  __TI_zero_init_nomemset              
000011df  __adddf3                             
00002537  __addsf3                             
00005530  __aeabi_ctype_table_                 
00005530  __aeabi_ctype_table_C                
00002e81  __aeabi_d2f                          
00003ae9  __aeabi_d2iz                         
00003cd9  __aeabi_d2uiz                        
000011df  __aeabi_dadd                         
00003381  __aeabi_dcmpeq                       
000033bd  __aeabi_dcmpge                       
000033d1  __aeabi_dcmpgt                       
000033a9  __aeabi_dcmple                       
00003395  __aeabi_dcmplt                       
00001c05  __aeabi_ddiv                         
000020d5  __aeabi_dmul                         
000011d5  __aeabi_dsub                         
20200878  __aeabi_errno                        
00004ccd  __aeabi_errno_addr                   
00003d9d  __aeabi_f2d                          
00002537  __aeabi_fadd                         
000033e5  __aeabi_fcmpeq                       
00003421  __aeabi_fcmpge                       
00003435  __aeabi_fcmpgt                       
0000340d  __aeabi_fcmple                       
000033f9  __aeabi_fcmplt                       
00002c75  __aeabi_fmul                         
0000252d  __aeabi_fsub                         
00004239  __aeabi_i2d                          
00003f89  __aeabi_i2f                          
000036d1  __aeabi_idiv                         
000028e7  __aeabi_idiv0                        
000036d1  __aeabi_idivmod                      
0000298b  __aeabi_ldiv0                        
00004505  __aeabi_llsl                         
0000441d  __aeabi_lmul                         
00004cd5  __aeabi_memcpy                       
00004cd5  __aeabi_memcpy4                      
00004cd5  __aeabi_memcpy8                      
00004c61  __aeabi_memset                       
00004c61  __aeabi_memset4                      
00004c61  __aeabi_memset8                      
000043f9  __aeabi_ui2d                         
00003d5d  __aeabi_uidiv                        
00003d5d  __aeabi_uidivmod                     
00004ba1  __aeabi_uldivmod                     
00004505  __ashldi3                            
ffffffff  __binit__                            
00003185  __cmpdf2                             
0000403d  __cmpsf2                             
00001c05  __divdf3                             
00003185  __eqdf2                              
0000403d  __eqsf2                              
00003d9d  __extendsfdf2                        
00003ae9  __fixdfsi                            
00003cd9  __fixunsdfsi                         
00004239  __floatsidf                          
00003f89  __floatsisf                          
000043f9  __floatunsidf                        
00002e01  __gedf2                              
00003fc5  __gesf2                              
00002e01  __gtdf2                              
00003fc5  __gtsf2                              
00003185  __ledf2                              
0000403d  __lesf2                              
00003185  __ltdf2                              
0000403d  __ltsf2                              
UNDEFED   __mpu_init                           
000020d5  __muldf3                             
0000441d  __muldi3                             
00004079  __muldsi3                            
00002c75  __mulsf3                             
00003185  __nedf2                              
0000403d  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000011d5  __subdf3                             
0000252d  __subsf3                             
00002e81  __truncdfsf2                         
000028e9  __udivmoddi4                         
00004385  _c_int00_noargs                      
20200000  _sys_memory                          
UNDEFED   _system_post_cinit                   
00004d01  _system_pre_init                     
00004cdd  abort                                
00003969  adc_getValue                         
00005300  asc2_0806                            
00004d10  asc2_1608                            
00003ddd  atoi                                 
ffffffff  binit                                
20200844  black                                
00003047  convertAnalogToDigital               
2020087c  delayTick                            
00003569  frexp                                
00003569  frexpl                               
20200660  gMotorAFrontBackup                   
20200700  gMotorBFrontBackup                   
202004f0  gTIMER_0Backup                       
00000000  interruptVectors                     
00002455  ldexp                                
00002455  ldexpl                               
000044c5  main                                 
00004463  memccpy                              
0000283d  normalizeAnalogValues                
00001771  qsort                                
00002455  scalbn                               
00002455  scalbnl                              
202005b0  sensor                               
20200880  uwTick                               
00004265  vsprintf                             
00004c41  wcslen                               
20200854  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000e8d  Task_Start                           
0000103d  SYSCFG_DL_GPIO_init                  
000011d5  __aeabi_dsub                         
000011d5  __subdf3                             
000011df  __adddf3                             
000011df  __aeabi_dadd                         
00001367  Task_IdleFunction                    
00001369  No_MCU_Ganv_Sensor_Init              
000014f1  PID_SProsc                           
00001771  qsort                                
000018a5  OLED_ShowChar                        
00001af5  OLED_Init                            
00001c05  __aeabi_ddiv                         
00001c05  __divdf3                             
00001d11  DL_Timer_initFourCCPWMMode           
00001e15  HD_Init                              
00001f05  DL_Timer_initTimerMode               
00001fed  Task_Motor_PID                       
000020d5  __aeabi_dmul                         
000020d5  __muldf3                             
000021b9  Get_Analog_value                     
00002299  Task_Key                             
00002379  DL_SYSCTL_configSYSPLL               
00002455  ldexp                                
00002455  ldexpl                               
00002455  scalbn                               
00002455  scalbnl                              
0000252d  __aeabi_fsub                         
0000252d  __subsf3                             
00002537  __addsf3                             
00002537  __aeabi_fadd                         
00002605  Motor_SetDuty                        
000026d9  Task_Add                             
0000278d  GROUP1_IRQHandler                    
0000283d  normalizeAnalogValues                
000028e7  __aeabi_idiv0                        
000028e9  __udivmoddi4                         
0000298b  __aeabi_ldiv0                        
0000298d  SYSCFG_DL_initPower                  
00002a29  Task_OLED                            
00002ac5  I2C_OLED_WR_Byte                     
00002b5d  SYSCFG_DL_MotorAFront_init           
00002be9  SYSCFG_DL_MotorBFront_init           
00002c75  __aeabi_fmul                         
00002c75  __mulsf3                             
00002d85  __TI_decompress_lzss                 
00002e01  __gedf2                              
00002e01  __gtdf2                              
00002e75  Sys_GetTick                          
00002e81  __aeabi_d2f                          
00002e81  __truncdfsf2                         
00002ef5  No_MCU_Ganv_Sensor_Init_Frist        
00002f69  Task_GraySensor                      
00002fd9  OLED_ShowString                      
00003047  convertAnalogToDigital               
000030b3  I2C_OLED_Clear                       
0000311d  Motor_Start                          
00003185  __cmpdf2                             
00003185  __eqdf2                              
00003185  __ledf2                              
00003185  __ltdf2                              
00003185  __nedf2                              
00003255  DL_SYSCTL_setHFCLKSourceHFXTParams   
000032b9  Key_Read                             
0000331d  Task_Init                            
00003381  __aeabi_dcmpeq                       
00003395  __aeabi_dcmplt                       
000033a9  __aeabi_dcmple                       
000033bd  __aeabi_dcmpge                       
000033d1  __aeabi_dcmpgt                       
000033e5  __aeabi_fcmpeq                       
000033f9  __aeabi_fcmplt                       
0000340d  __aeabi_fcmple                       
00003421  __aeabi_fcmpge                       
00003435  __aeabi_fcmpgt                       
00003449  I2C_OLED_i2c_sda_unlock              
000034a9  SYSCFG_DL_I2C_OLED_init              
00003509  DL_I2C_fillControllerTXFIFO          
00003569  frexp                                
00003569  frexpl                               
00003621  __TI_ltoa                            
000036d1  __aeabi_idiv                         
000036d1  __aeabi_idivmod                      
0000377d  SYSCFG_DL_ADC1_init                  
000037d1  SYSCFG_DL_SYSCTL_init                
000038c9  Interrupt_Init                       
00003969  adc_getValue                         
00003a05  OLED_Printf                          
00003ae9  __aeabi_d2iz                         
00003ae9  __fixdfsi                            
00003b33  Motor_GetSpeed                       
00003b7d  SYSCFG_DL_init                       
00003c0d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003c95  No_Mcu_Ganv_Sensor_Task_Without_tick 
00003cd9  __aeabi_d2uiz                        
00003cd9  __fixunsdfsi                         
00003d1d  DL_ADC12_setClockConfig              
00003d5d  __aeabi_uidiv                        
00003d5d  __aeabi_uidivmod                     
00003d9d  __aeabi_f2d                          
00003d9d  __extendsfdf2                        
00003ddd  atoi                                 
00003f11  Get_Anolog_Value                     
00003f4d  I2C_OLED_Set_Pos                     
00003f89  __aeabi_i2f                          
00003f89  __floatsisf                          
00003fc5  __gesf2                              
00003fc5  __gtsf2                              
00004001  __TI_auto_init_nobinit_nopinit       
0000403d  __cmpsf2                             
0000403d  __eqsf2                              
0000403d  __lesf2                              
0000403d  __ltsf2                              
0000403d  __nesf2                              
00004079  __muldsi3                            
0000411d  SYSCFG_DL_TIMER_0_init               
00004181  _IQ24toF                             
000041e1  _IQ24mpy                             
00004239  __aeabi_i2d                          
00004239  __floatsidf                          
00004265  vsprintf                             
00004291  PID_Init                             
0000435d  SysTick_Increasment                  
00004385  _c_int00_noargs                      
000043d3  DL_I2C_setClockConfig                
000043f9  __aeabi_ui2d                         
000043f9  __floatunsidf                        
0000441d  __aeabi_lmul                         
0000441d  __muldi3                             
00004441  PID_SetParams                        
00004463  memccpy                              
000044a5  Delay                                
000044c5  main                                 
00004505  __aeabi_llsl                         
00004505  __ashldi3                            
000046e5  DL_Timer_setCaptCompUpdateMethod     
00004701  DL_Timer_setClockConfig              
0000471d  TIMA0_IRQHandler                     
000049f1  DL_Timer_setCaptureCompareOutCtl     
00004ad5  __TI_zero_init_nomemset              
00004ba1  __aeabi_uldivmod                     
00004bdb  TI_memcpy_small                      
00004bed  __TI_decompress_none                 
00004c21  DL_Timer_setCaptureCompareValue      
00004c31  SYSCFG_DL_SYSTICK_init               
00004c41  wcslen                               
00004c51  Get_Digtal_For_User                  
00004c61  __aeabi_memset                       
00004c61  __aeabi_memset4                      
00004c61  __aeabi_memset8                      
00004c7d  TI_memset_small                      
00004c8b  DL_Common_delayCycles                
00004cc5  SysTick_Handler                      
00004ccd  __aeabi_errno_addr                   
00004cd5  __aeabi_memcpy                       
00004cd5  __aeabi_memcpy4                      
00004cd5  __aeabi_memcpy8                      
00004cdd  abort                                
00004ce3  ADC0_IRQHandler                      
00004ce3  ADC1_IRQHandler                      
00004ce3  AES_IRQHandler                       
00004ce3  CANFD0_IRQHandler                    
00004ce3  DAC0_IRQHandler                      
00004ce3  DMA_IRQHandler                       
00004ce3  Default_Handler                      
00004ce3  GROUP0_IRQHandler                    
00004ce3  HardFault_Handler                    
00004ce3  I2C0_IRQHandler                      
00004ce3  I2C1_IRQHandler                      
00004ce3  NMI_Handler                          
00004ce3  PendSV_Handler                       
00004ce3  RTC_IRQHandler                       
00004ce3  SPI0_IRQHandler                      
00004ce3  SPI1_IRQHandler                      
00004ce3  SVC_Handler                          
00004ce3  TIMA1_IRQHandler                     
00004ce3  TIMG0_IRQHandler                     
00004ce3  TIMG12_IRQHandler                    
00004ce3  TIMG6_IRQHandler                     
00004ce3  TIMG7_IRQHandler                     
00004ce3  TIMG8_IRQHandler                     
00004ce3  UART0_IRQHandler                     
00004ce3  UART1_IRQHandler                     
00004ce3  UART2_IRQHandler                     
00004ce3  UART3_IRQHandler                     
00004ce6  C$$EXIT                              
00004ce7  HOSTexit                             
00004ceb  Reset_Handler                        
00004d01  _system_pre_init                     
00004d10  asc2_1608                            
00005300  asc2_0806                            
00005530  __aeabi_ctype_table_                 
00005530  __aeabi_ctype_table_C                
00005750  __TI_Handler_Table_Base              
0000575c  __TI_Handler_Table_Limit             
00005764  __TI_CINIT_Base                      
00005774  __TI_CINIT_Limit                     
00005774  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  _sys_memory                          
202004f0  gTIMER_0Backup                       
202005ac  ExISR_Flag                           
202005b0  sensor                               
20200660  gMotorAFrontBackup                   
20200700  gMotorBFrontBackup                   
202007a1  HD_count                             
202007a4  Motor_Font_Left                      
202007ec  Motor_Font_Right                     
20200834  Analog                               
20200844  black                                
20200854  white                                
20200864  Motor                                
2020086c  Data_MotorEncoder                    
20200870  Data_Motor_TarSpeed                  
20200874  Data_Tracker_Offset                  
20200878  __aeabi_errno                        
2020087c  delayTick                            
20200880  uwTick                               
20200884  Digtal                               
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[245 symbols]
