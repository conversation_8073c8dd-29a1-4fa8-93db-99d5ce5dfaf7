# PRD - 七路循迹传感器修正算法优化

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-08-01 |
| 负责人 | Emma (产品经理) |
| 项目名称 | TI_CAR1.9 七路循迹传感器修正算法优化 |
| 文档状态 | 需求分析完成 |

## 2. 背景与问题陈述

### 2.1 当前实现分析

#### 2.1.1 现有循迹传感器配置
- **传感器数量**: 7路数字传感器 (Track_L1 ~ Track_L7)
- **传感器间距**: 1.5cm (DIS_INRERVAL = _IQ(1.5))
- **传感器位置权重**: 
  - 索引0-6对应位置: -4.5, -3.0, -1.5, 0, 1.5, 3.0, 4.5 cm
  - 计算公式: `(i - 3.0f) * 1.5`

#### 2.1.2 当前修正算法
```c
// 当前转向系数
#define INDEX 1.5f

// 修正计算
_iq Steering_Adjustment = _IQmpy(Data_Tracker_Offset, _IQ(INDEX));
_iq Left_Speed = Data_Motor_TarSpeed + Steering_Adjustment;
_iq Right_Speed = Data_Motor_TarSpeed - Steering_Adjustment;
```

### 2.2 问题识别
1. **修正幅度过大**: INDEX=1.5可能导致转向过于激烈
2. **缺乏渐进式修正**: 没有根据偏差大小调整修正强度
3. **未充分利用七路传感器**: 算法相对简单，未发挥多传感器优势

## 3. 目标与成功指标

### 3.1 项目目标
- **主要目标**: 优化七路循迹传感器的修正算法，降低修正幅度，提升循迹稳定性
- **次要目标**: 提供可调节的修正参数，便于现场调试

### 3.2 关键结果(KRs)
- **KR1**: 修正系数降低至合理范围 (0.5-1.0)
- **KR2**: 实现分级修正算法 (小偏差小修正，大偏差大修正)
- **KR3**: 保持循迹精度的同时提升行驶稳定性

### 3.3 成功指标
- **稳定性指标**: 减少不必要的左右摆动
- **精度指标**: 保持原有的循迹精度
- **响应性指标**: 对大偏差能够及时响应

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**: 智能车开发者和调试人员
- **使用场景**: 循迹比赛、自动驾驶测试

### 4.2 用户故事
- **故事1**: 作为开发者，我希望循迹车能够平稳行驶，减少不必要的摆动
- **故事2**: 作为调试人员，我希望能够方便地调整修正参数
- **故事3**: 作为用户，我希望在不同赛道条件下都能获得稳定的循迹效果

## 5. 功能规格详述

### 5.1 优化后的修正算法设计

#### 5.1.1 分级修正策略
```c
// 新的修正系数定义
#define INDEX_SMALL  0.6f   // 小偏差修正系数
#define INDEX_MEDIUM 0.8f   // 中等偏差修正系数  
#define INDEX_LARGE  1.0f   // 大偏差修正系数

// 偏差阈值定义
#define OFFSET_SMALL_THRESHOLD  _IQ(1.0)   // 小偏差阈值 1cm
#define OFFSET_MEDIUM_THRESHOLD _IQ(2.5)   // 中等偏差阈值 2.5cm
```

#### 5.1.2 自适应修正算法
```c
_iq Calculate_Adaptive_Correction(_iq offset) {
    _iq abs_offset = _IQabs(offset);
    _iq correction_factor;
    
    if (abs_offset <= OFFSET_SMALL_THRESHOLD) {
        correction_factor = _IQ(INDEX_SMALL);
    } else if (abs_offset <= OFFSET_MEDIUM_THRESHOLD) {
        correction_factor = _IQ(INDEX_MEDIUM);
    } else {
        correction_factor = _IQ(INDEX_LARGE);
    }
    
    return _IQmpy(offset, correction_factor);
}
```

### 5.2 传感器权重优化

#### 5.2.1 当前权重分析
- 传感器0-6位置: -4.5, -3.0, -1.5, 0, 1.5, 3.0, 4.5 cm
- 问题: 权重计算公式 `(i - 3.0f) * 1.5` 中心偏移

#### 5.2.2 优化权重计算
```c
// 优化后的权重计算 - 确保中心传感器权重为0
_iq sensor_pos = _IQmpy(_IQ(i - 3), DIS_INRERVAL);
// 结果: -4.5, -3.0, -1.5, 0, 1.5, 3.0, 4.5 cm
```

### 5.3 滤波优化

#### 5.3.1 当前滤波参数
```c
const _iq Filter_Value = _IQ(0.7); // 当前滤波系数
```

#### 5.3.2 优化滤波策略
```c
// 自适应滤波 - 根据偏差变化调整滤波强度
_iq Calculate_Adaptive_Filter(_iq current_offset, _iq last_offset) {
    _iq offset_change = _IQabs(current_offset - last_offset);
    
    if (offset_change > _IQ(2.0)) {
        return _IQ(0.5); // 快速响应大变化
    } else {
        return _IQ(0.8); // 平滑小变化
    }
}
```

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- ✅ 修正系数优化 (降低修正幅度)
- ✅ 分级修正算法实现
- ✅ 传感器权重计算优化
- ✅ 自适应滤波算法
- ✅ 参数可配置化

### 6.2 排除功能 (Out of Scope)
- ❌ 传感器硬件修改
- ❌ 基础循迹逻辑重构
- ❌ 电机控制算法修改
- ❌ 新增传感器类型支持

## 7. 依赖与风险

### 7.1 内部依赖
- **代码依赖**: BSP/Src/Tracker.c, APP/Src/Task_App.c
- **硬件依赖**: 七路循迹传感器正常工作
- **算法依赖**: IQmath库支持

### 7.2 潜在风险
- **风险1**: 修正参数调整可能影响循迹精度
- **风险2**: 算法复杂度增加可能影响实时性
- **风险3**: 参数配置错误可能导致循迹失效

### 7.3 风险缓解策略
- **策略1**: 保留原有算法作为备选方案
- **策略2**: 提供参数重置功能
- **策略3**: 增加算法性能监控

## 8. 发布初步计划

### 8.1 开发阶段
1. **阶段1**: 算法设计与仿真验证 (1天)
2. **阶段2**: 代码实现与单元测试 (1天)  
3. **阶段3**: 集成测试与参数调优 (1天)
4. **阶段4**: 现场测试与性能验证 (1天)

### 8.2 验收标准
- **功能验收**: 所有新算法功能正常工作
- **性能验收**: 循迹稳定性提升，修正幅度合理
- **兼容性验收**: 与现有系统完全兼容
- **文档验收**: 完整的技术文档和使用说明

## 9. 附录

### 9.1 当前代码分析
- **文件位置**: BSP/Src/Tracker.c (第64-82行)
- **关键函数**: Tracker_Read()
- **修正位置**: APP/Src/Task_App.c (第122-128行)
- **关键参数**: INDEX = 1.5f

### 9.2 技术参考
- **IQmath库**: 定点数运算支持
- **传感器规格**: 数字输出，1.5cm间距
- **控制周期**: 20ms循迹任务周期
