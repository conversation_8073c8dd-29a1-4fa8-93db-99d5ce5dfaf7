# 电机卡顿问题根因分析报告

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-08-01 |
| 负责人 | Alex (工程师) |
| 项目名称 | TI_CAR1.9 中断导致电机卡顿问题分析 |
| 文档状态 | 根因定位完成 |

## 2. 问题现象

### 2.1 用户反馈
- **现象**: 使用定时器中断后，电机出现"一卡一卡"的运动
- **对比**: 之前使用任务调度时电机运行平滑
- **疑问**: 为什么中断方式会导致电机卡顿

## 3. 🎯 **根本原因分析**

### 3.1 **核心问题：PID计算频率与编码器清零冲突**

#### 问题机制分析

<augment_code_snippet path="BSP/Src/Motor.c" mode="EXCERPT">
```c
bool Motor_GetSpeed(MOTOR_Def_t *Motor, uint16_t time)
{
    if (Motor == NULL) return false;

    Motor->Motor_PID_Instance.Acutal_Now = *Motor->Motor_Encoder_Addr;
    *Motor->Motor_Encoder_Addr = 0;  // ❌ 关键问题：立即清零编码器

    return true;
}
```
</augment_code_snippet>

#### 时序冲突分析

**原任务调度方式** (平滑运行):
```
时间轴: 0ms ----10ms----20ms----30ms----40ms----50ms
Tracker:  ↑      ↑      ↑      ↑      ↑      ↑    (10ms周期)
Motor:    ↑             ↑             ↑           (注释掉，不执行)
编码器:   累积→  累积→  累积→  累积→  累积→  累积→
```

**中断方式** (卡顿):
```
时间轴: 0ms ----10ms----20ms----30ms----40ms----50ms
Tracker:  ↑      ↑      ↑      ↑      ↑      ↑    (10ms周期)
Motor:    ↑             ↑             ↑           (20ms中断)
编码器:   累积→  清零!   累积→  清零!   累积→  清零!
```

### 3.2 **问题1：编码器数据丢失**

#### 数据丢失机制
1. **0-10ms**: 编码器累积脉冲数 (例如: 50个脉冲)
2. **10ms**: `Tracker_Read`执行，但不影响编码器
3. **20ms**: `Task_Motor_PID`中断执行
   - `Motor_GetSpeed`读取编码器值: 100个脉冲
   - **立即清零编码器**: `*Motor->Motor_Encoder_Addr = 0`
4. **20-30ms**: 编码器重新开始累积
5. **30ms**: `Tracker_Read`执行，编码器继续累积
6. **40ms**: `Task_Motor_PID`再次执行
   - 只能读取到20ms的累积值，丢失了完整的速度信息

#### 速度计算错误
<augment_code_snippet path="BSP/Src/PID.c" mode="EXCERPT">
```c
void PID_SProsc(PID_Def_t *pid)
{
    pid->Err_Last = pid->Err_Now;
    pid->Acutal_Last = pid->Acutal_Now;
    pid->Err_Now = pid->Target - pid->Acutal_Now;  // ❌ Acutal_Now数据不准确
    
    // PID计算基于错误的速度反馈
    pid->Out = pid->Kp * pid->Err_Now + pid->Ki * pid->Err_Int + pid->Dif_Out;
}
```
</augment_code_snippet>

### 3.3 **问题2：PID参数不匹配新频率**

#### 参数配置分析
<augment_code_snippet path="BSP/Src/Motor.c" mode="EXCERPT">
```c
// 当前PID参数 (为10ms周期设计)
PID_SetParams(&Motor_Font_Left.Motor_PID_Instance, 8.9f, 1.2, 0.0f);
PID_SetParams(&Motor_Font_Right.Motor_PID_Instance, 8.9f, 1.2, 0.0f);
```
</augment_code_snippet>

#### 频率变化影响
- **原设计**: 10ms周期，Kp=8.9, Ki=1.2
- **现在**: 20ms周期，但参数未调整
- **结果**: 
  - 积分项累积过快 (Ki=1.2对20ms周期过大)
  - 比例项响应过强 (Kp=8.9对不准确的速度反馈过敏感)

### 3.4 **问题3：中断与编码器中断冲突**

#### 中断优先级冲突
<augment_code_snippet path="APP/Src/Interrupt.c" mode="EXCERPT">
```c
void GROUP1_IRQHandler(void)
{
    // 编码器中断处理
    if (ISR_IS_GPIO(SPD_READER_A_FONT_LEFT_A_PIN)) //左前轮
    {
        if (GET_RDR_B_VAL(SPD_READER_B_FONT_LEFT_B_PIN)) (*Motor_Font_Left.Motor_Encoder_Addr)++;
        else (*Motor_Font_Left.Motor_Encoder_Addr)--;
        SPD_READER_CLR_ISR_FLAG(SPD_READER_A_FONT_LEFT_A_PIN);
    }
}
```
</augment_code_snippet>

#### 冲突机制
1. **编码器中断**: 随时可能发生，更新编码器计数
2. **定时器中断**: 20ms周期，执行PID计算
3. **冲突**: 当定时器中断执行`Motor_GetSpeed`时，可能与编码器中断产生竞争条件

## 4. 卡顿现象解释

### 4.1 **卡顿产生机制**

#### 速度反馈波动
```
实际电机速度: ————————————————————— (恒定)
PID读取速度: ↗️↘️↗️↘️↗️↘️↗️↘️↗️↘️ (波动)
PID输出:     ↗️↘️↗️↘️↗️↘️↗️↘️↗️↘️ (波动)
电机表现:    卡顿卡顿卡顿卡顿卡顿
```

#### 具体数值示例
```
时刻    编码器累积    PID读取    误差计算    输出    电机表现
0ms     0            -          -          -       -
10ms    50           -          -          -       -
20ms    100          100        大误差     高输出   突然加速
20ms    0(清零)      -          -          -       -
30ms    30           -          -          -       -
40ms    60           60         小误差     低输出   突然减速
40ms    0(清零)      -          -          -       -
```

### 4.2 **为什么任务调度方式平滑**

#### 任务调度时的状态
<augment_code_snippet path="APP/Src/Task_App.c" mode="EXCERPT">
```c
void Task_Init(void)
{
    // Task_Add("Motor", Task_Motor_PID, 10, NULL, 0);  // ❌ 被注释掉
    Task_Add("Key", Task_Key, 20, NULL, 2);
    Task_Add("OLED", Task_OLED, 1000, NULL, 3);
    Task_Add("Tracker", Task_Tracker, 10, NULL, 1);
}
```
</augment_code_snippet>

**关键发现**: 在任务调度方式下，`Task_Motor_PID`实际上是**被注释掉的**，根本没有执行！

#### 平滑运行的真实原因
1. **没有PID控制**: `Task_Motor_PID`未执行，电机目标值保持初始状态
2. **没有编码器清零**: 编码器数据不被干扰
3. **电机惯性运行**: 电机按照初始PWM值或最后设置值运行
4. **用户误解**: 以为是PID控制平滑，实际是没有控制

## 5. 解决方案

### 5.1 🔴 **方案A：修复编码器读取逻辑 (推荐)**

#### 修改编码器读取方式
```c
// 修改 Motor_GetSpeed 函数
bool Motor_GetSpeed(MOTOR_Def_t *Motor, uint16_t time)
{
    if (Motor == NULL) return false;

    // 方案1：基于时间间隔计算速度
    static uint32_t last_time = 0;
    uint32_t current_time = Sys_GetTick();
    uint32_t time_diff = current_time - last_time;
    
    if (time_diff >= 20) {  // 20ms间隔
        Motor->Motor_PID_Instance.Acutal_Now = *Motor->Motor_Encoder_Addr;
        *Motor->Motor_Encoder_Addr = 0;  // 只在正确时机清零
        last_time = current_time;
    }
    
    return true;
}
```

### 5.2 🟡 **方案B：调整PID参数**

#### 针对20ms周期优化参数
```c
// 修改 BSP/Src/Motor.c 第80-81行
// 原参数 (10ms周期设计)
PID_SetParams(&Motor_Font_Left.Motor_PID_Instance, 8.9f, 1.2, 0.0f);

// 新参数 (20ms周期优化)
PID_SetParams(&Motor_Font_Left.Motor_PID_Instance, 4.5f, 0.6f, 0.1f);
PID_SetParams(&Motor_Font_Right.Motor_PID_Instance, 4.5f, 0.6f, 0.1f);
```

**参数调整理由**:
- **Kp减半**: 8.9→4.5，适应20ms周期
- **Ki减半**: 1.2→0.6，避免积分饱和
- **添加Kd**: 0→0.1，增加系统阻尼

### 5.3 🟢 **方案C：回归任务调度 (最简单)**

#### 恢复任务调度方式
```c
// 修改 APP/Src/Task_App.c 第56行
// 取消注释
Task_Add("Motor", Task_Motor_PID, 20, NULL, 0);  // 恢复20ms任务调度

// 同时删除中断处理函数
// 注释掉 TIMER_0_INST_IRQHandler 函数
```

## 6. 推荐修改步骤

### 6.1 **第一步：立即缓解 (调整PID参数)**
```c
// 在 BSP/Src/Motor.c 第80-81行修改
PID_SetParams(&Motor_Font_Left.Motor_PID_Instance, 4.0f, 0.5f, 0.1f);
PID_SetParams(&Motor_Font_Right.Motor_PID_Instance, 4.0f, 0.5f, 0.1f);
```

### 6.2 **第二步：根本解决 (选择一种)**

#### 选项A：修复编码器逻辑
- 修改`Motor_GetSpeed`函数，添加时间间隔控制

#### 选项B：回归任务调度
- 恢复`Task_Add("Motor", Task_Motor_PID, 20, NULL, 0);`
- 注释掉中断处理函数

## 7. 总结

### 7.1 **问题根因**
1. **编码器数据丢失**: 20ms中断频繁清零编码器，导致速度反馈不准确
2. **PID参数不匹配**: 原参数为10ms周期设计，不适合20ms周期
3. **中断竞争条件**: 定时器中断与编码器中断可能产生冲突

### 7.2 **卡顿机制**
- 不准确的速度反馈 → PID输出波动 → 电机PWM波动 → 卡顿现象

### 7.3 **解决核心**
- **立即**: 调整PID参数适应新频率
- **根本**: 修复编码器读取逻辑或回归任务调度

**您的中断配置技术上正确，但需要配套的参数调整和逻辑修复！**
