# 循迹传感器校准模块集成指南

## 📋 集成步骤

### 1. 修改 APP/Src/Task_App.c

```c
// 在文件顶部添加头文件
#include "Tracker_Calibration.h"

// 在 Task_Init() 函数中添加
void Task_Init(void)
{
    Motor_Start();
    OLED_Init();
    
    // 添加传感器校准初始化
    Tracker_Calibration_Init();  // ✅ 新增
    
    Interrupt_Init();
    
    // 添加校准任务到任务调度器
    Task_Add(Tracker_Calibration_Task, 0, 100);  // ✅ 新增：100ms周期
    
    // 其他现有任务...
    Task_Add(Task_Motor_PID, 0, 20);
    Task_Add(Task_OLED, 0, 200);
    Task_Add(Task_Key, 0, 50);
}

// 修改 Task_Key() 函数
void Task_Key(void)
{
    Key_Scan();
    
    if (Key_Val != 0) {
        // 检查是否在校准状态
        if (Tracker_Calibration_GetState() != TRACKER_CALIB_IDLE) {
            // 校准模式下，按键事件交给校准模块处理
            Tracker_Calibration_KeyHandler(Key_Val);  // ✅ 新增
        } else {
            // 正常模式下的按键处理
            if (Key_Val == 1) {
                Data_Motor_TarSpeed += 100;
                if (Data_Motor_TarSpeed > 2000) Data_Motor_TarSpeed = 2000;
            }
            else if (Key_Val == 2) {
                // 按键2：开始校准或正常功能
                if (!Tracker_Calibration_IsComplete()) {
                    Tracker_Calibration_Start();  // ✅ 新增：开始校准
                } else {
                    // 正常功能处理
                    Data_Motor_TarSpeed -= 100;
                    if (Data_Motor_TarSpeed < 0) Data_Motor_TarSpeed = 0;
                }
            }
        }
        
        Key_Val = 0;  // 清除按键值
    }
}
```

### 2. 修改 BSP/Src/Tracker.c

```c
// 在原有 HD_Init() 函数前添加注释
/*
// ⚠️ 原有阻塞式校准函数 - 已被状态机版本替代
// 保留作为参考，实际使用 Tracker_Calibration 模块
void HD_Init(void){
    // ... 原有代码
}
*/

// 添加新的非阻塞初始化函数
void HD_Init_NonBlocking(void)
{
    // 基础硬件初始化（保留原有逻辑）
    No_MCU_Ganv_Sensor_Init_Frist(&sensor);
    No_Mcu_Ganv_Sensor_Task_Without_tick(&sensor);
    Get_Anolog_Value(&sensor, Analog);
    
    // 校准流程由状态机模块处理
    // 调用 Tracker_Calibration_Start() 开始校准
}

// 修改按键处理部分（如果存在独立的按键处理）
/*
// ⚠️ 原有按键处理 - 已集成到任务系统
if (Key_Val==2)
{
    HD_count++;
    if(HD_count>3)HD_count=0;
}
*/
```

### 3. 修改 main.c（如果需要）

```c
#include "Tracker_Calibration.h"

int main(void)
{
    SYSCFG_DL_init();
    Task_Init();        // 包含了 Tracker_Calibration_Init()
    Task_Start();       // 开始任务调度
    
    while(1) {
        // 主循环由任务调度器处理
        // 校准功能完全集成到任务系统中
    }
}
```

## 🎯 使用方法

### 用户操作流程：
1. **系统上电** → 自动初始化传感器硬件
2. **按下Key2** → 开始校准流程
3. **放置白线** → 按Key2开始白线采样
4. **等待采样完成** → 系统提示放置黑线
5. **放置黑线** → 按Key2开始黑线采样  
6. **校准完成** → 按Key2退出校准模式

### 开发者接口：
```c
// 检查校准状态
if (Tracker_Calibration_IsComplete()) {
    // 校准已完成，可以开始循迹
}

// 获取校准数据
uint16_t white_data[8], black_data[8];
if (Tracker_Calibration_GetData(white_data, black_data)) {
    // 使用校准数据进行循迹算法
}

// 手动开始校准
Tracker_Calibration_Start();

// 获取当前状态
TrackerCalibState_t state = Tracker_Calibration_GetState();
```

## ✅ 优势对比

| 特性 | 原有方案 | 新方案 |
|------|----------|--------|
| **系统响应** | 阻塞式，系统卡死 | 非阻塞，任务正常运行 |
| **代码结构** | 分散，难维护 | 模块化，易维护 |
| **用户体验** | 操作不直观 | 清晰的状态提示 |
| **错误处理** | 无错误恢复 | 完整的错误处理 |
| **扩展性** | 难以扩展 | 易于添加新功能 |
| **调试性** | 难以调试 | 状态清晰，易调试 |

## 🔧 配置参数

可以通过修改 `g_tracker_config` 调整校准参数：

```c
// 在 Tracker_Calibration.c 中修改
TrackerCalibConfig_t g_tracker_config = {
    .sample_interval_ms = 100,      // 采样间隔：100ms
    .samples_per_calib = 5,         // 每次校准采样次数：5次
    .display_update_ms = 200        // 显示更新间隔：200ms
};
```

## 🚀 扩展功能建议

1. **自动校准**：添加自动检测白线/黑线的功能
2. **校准数据保存**：将校准数据保存到Flash中
3. **多点校准**：支持多个位置的校准数据
4. **校准质量检测**：检测校准数据的有效性

## 📝 注意事项

1. **时间戳函数**：需要实现 `Tracker_GetTick()` 函数，返回毫秒时间戳
2. **任务优先级**：校准任务建议使用较低优先级，避免影响关键任务
3. **内存使用**：新方案增加约200字节RAM使用
4. **兼容性**：完全兼容原有传感器驱动接口

---

**集成完成后，您的传感器校准功能将完美融入任务调度框架，提供流畅的用户体验！**
